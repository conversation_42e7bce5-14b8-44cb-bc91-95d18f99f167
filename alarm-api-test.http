### 告警配置管理 API 测试文件

### 变量定义
@baseUrl = http://localhost:8808/api
@token = H4sIAAAAAAAAABWN23KCMAAFv6gOSQzqI1ZQUEKBJBReOgJSYgJEqnL5+urrntmzl8mr830hAuHFbHYBEd5mcZm8uUzcF3THYJcuCU0R2dnLgBZTQNlAqD+5YhDlN1GFxHWesPcBOu8dmSfq8d6KZvOXMSXdayd8ao1vlibkmiP+KB2CCxSpnL98RHQOsZd9uqYreZ3OWcvtUdH9/VUxcEndkTo1vkinChjY+chLImBNvswiZuCON1r68G77bXQKVTGQlp/z2IAUaMFRdKKNFFW4SPveyobfiwmmantyChbgsn1uofCgUvdzH5tdZB9GGD9/dmujSyPby7Qx8K3d21tkrmH5vaKt35nH1TJkTFtrtnT0J0rKjXF7Kn0EqwmejY+4m4Biukvl8wFoF7bHnow/pwOz9ny+muY1+VJNpbJxIxoMK7sMcbIGf3oW4W08GENN0n+mkMRgkwEAAA==
@projectId = 931806a640de4f87b6ef5f18de558213
@alarmId = 47
@notificationId = 46

### =========== AlarmConfigController 测试用例 ===========

### 1. 查询告警配置列表
GET {{baseUrl}}/alarm/config/list
Authorization: {{token}}
Projectid: {{projectId}}
Content-Type: application/json

###

### 2. 新增或更新告警配置 - 新增场景
POST {{baseUrl}}/alarm/config/switch
Authorization: {{token}}
Projectid: {{projectId}}
Content-Type: application/json

{
  "alarmContent": 0,
  "isEnabled": true,
  "profitDeviationCoefficient": 0.1,
  "downtimeThreshold": 30,
  "offlineTimeThreshold": 60,
  "efficiencyReminderThreshold": 0.8
}

###

### 3. 新增或更新告警配置 - 更新场景
POST {{baseUrl}}/alarm/config/switch
Authorization: {{token}}
Projectid: {{projectId}}
Content-Type: application/json

{
  "id": 11,
  "alarmContent": 0,
  "isEnabled": true,
  "profitDeviationCoefficient": 0.15,
  "downtimeThreshold": 45,
  "offlineTimeThreshold": 90,
  "efficiencyReminderThreshold": 0.85
}

###

### 4. 批量新增告警通知配置
POST {{baseUrl}}/alarm/notification/batch/add
Authorization: {{token}}
Content-Type: application/json

{
  "projectIds": ["4f537620d37d40e19dd25be5ca6ad941"],
  "alarmContent": 5,
  "isEnabled": true,
  "efficiencyReminderThreshold": 1.2,
  "userName": "申潞杰",
  "notificationType": "0",
  "email": "<EMAIL>"
}

###

### 5. 批量编辑告警通知配置 - 编辑告警内容
POST {{baseUrl}}/alarm/notification/batch/edit
Authorization: {{token}}
Projectid: {{projectId}}
Content-Type: application/json

{
  "projectIds": ["3931643d564f47fdb1c8548576df3108", "9d68ecb8873f4f5d84d7eb158c4e2149"],
  "alarmContent": 3,
  "isEnabled": true,
  "profitDeviationCoefficient": 0.15,
  "downtimeThreshold": 45,
  "offlineTimeThreshold": 90,
  "efficiencyReminderThreshold": 0.85
}

###

### 6. 批量编辑告警通知配置 - 替换通知人员
POST {{baseUrl}}/alarm/notification/batch/edit
Authorization: {{token}}
Projectid: {{projectId}}
Content-Type: application/json

{
  "projectIds": ["3931643d564f47fdb1c8548576df3108", "9d68ecb8873f4f5d84d7eb158c4e2149"],
  "originalUserName": "李四",
  "newUserName": "李四",
  "originalPhone": "13800138000",
  "newPhone": "13900139000",
  "originalEmail": "<EMAIL>",
  "newEmail": "<EMAIL>"
}

###

### 7. 批量编辑告警通知配置 - 同时编辑告警内容和替换人员
POST {{baseUrl}}/alarm/notification/batch/edit
Authorization: {{token}}
Projectid: {{projectId}}
Content-Type: application/json

{
  "projectIds": ["3931643d564f47fdb1c8548576df3108", "9d68ecb8873f4f5d84d7eb158c4e2149"],
  "alarmContent": 3,
  "isEnabled": false,
  "profitDeviationCoefficient": 0.2,
  "downtimeThreshold": 60,
  "offlineTimeThreshold": 120,
  "efficiencyReminderThreshold": 0.9,
  "originalUserName": "李四",
  "newUserName": "王五",
  "originalPhone": "13900139000",
  "newPhone": "13700137000",
  "originalEmail": "<EMAIL>",
  "newEmail": "<EMAIL>"
}

###

### 8. 批量删除告警通知配置 - 按用户名删除
POST {{baseUrl}}/alarm/notification/batch/delete
Authorization: {{token}}
Projectid: {{projectId}}
Content-Type: application/json

{
  "projectIds": ["3931643d564f47fdb1c8548576df3108", "9d68ecb8873f4f5d84d7eb158c4e2149"],
  "userName": "王五"
}

###

### 9. 批量删除告警通知配置 - 按手机号删除
POST {{baseUrl}}/alarm/notification/batch/delete
Authorization: {{token}}
Projectid: {{projectId}}
Content-Type: application/json

{
  "projectIds": ["3931643d564f47fdb1c8548576df3108", "9d68ecb8873f4f5d84d7eb158c4e2149"],
  "phone": "13800138000"
}

###

### 10. 批量删除告警通知配置 - 按邮箱删除
POST {{baseUrl}}/alarm/notification/batch/delete
Authorization: {{token}}
Projectid: {{projectId}}
Content-Type: application/json

{
  "projectIds": ["3931643d564f47fdb1c8548576df3108", "9d68ecb8873f4f5d84d7eb158c4e2149"],
  "email": "<EMAIL>"
}

###

### 11. 批量删除告警通知配置 - 多条件删除
POST {{baseUrl}}/alarm/notification/batch/delete
Authorization: {{token}}
Projectid: {{projectId}}
Content-Type: application/json

{
  "projectIds": ["project-1", "project-2"],
  "userName": "张三",
  "phone": "13800138000",
  "email": "<EMAIL>"
}

###

### =========== AlarmNotificationController 测试用例 ===========

### 12. 根据告警ID查询通知配置列表
GET {{baseUrl}}/alarm/notification/list?alarmId={{alarmId}}
Authorization: {{token}}
Projectid: {{projectId}}
Content-Type: application/json

###

### 14. 新增告警通知配置
POST {{baseUrl}}/alarm/notification/add
Authorization: {{token}}
Projectid: {{projectId}}
Content-Type: application/json

{
  "userName": "dsa",
  "notificationType": "0",
  "phone": null,
  "email": "<EMAIL>",
  "alarmId": "47"
}

###

### 18. 更新告警通知配置
POST {{baseUrl}}/alarm/notification/update
Authorization: {{token}}
Projectid: {{projectId}}
Content-Type: application/json

{
  "id": {{notificationId}},
  "alarmId": {{alarmId}},
  "userName": "fsd",
  "notificationType": "0",
  "phone": "13600136001",
  "email": "<EMAIL>"
}

###

### 20. 删除告警通知配置
POST {{baseUrl}}/alarm/notification/delete/15
Authorization: {{token}}
Projectid: {{projectId}}
Content-Type: application/json