TIME_OVERLAPPED=Times overlapped
ORDER_NUM_REPEAT=order num is exist
URL_DEL=delete project url
URL_UPDATE=update project url
DEVICE_NOT_EXISTS=device is not exists
DATE_OVERLAPPED=Dates overlapped
REPEAT_IMPORT=Duplicate imports
PASSWORD_ARE_INCONSISTENT=Passwords do not match
USER_PASSWORD_MISTYPED=Incorrect user password
INTERNAL_SERVER_ERROR=System error
ILLEGAL_ARGUMENT=Parameter format error
MANAGE_PROJECT_URL_UPDATE=update project url
DISTRIBUTION_FAILED=Failed to distribute
USER_EXISTS=Username already exists
SERVICE_EXCEPTION=Operation error
ILLEGAL_DATA=Data error
MANAGE_PROJECT_URL_DELETE=delete project url
MULTIPART_TOO_LARGE=File too large
ILLEGAL_STATE=Invalid status
MISSING_ARGUMENT=Missing parameters
ILLEGAL_ACCESS=Illegal access
LOGIN_FAIL=Failed to log in
PWD_INVALID=Wrong username or password
USER_INVALID=Wrong username or password
DATA_REFERENCED=Permissions not cleared
UNAUTHORIZED=Insufficient permissions
NO_DATA=No data available
METHOD_NOT_ALLOWED=Unsupported method
ILLEGAL_ARGUMENT_TYPE=Wrong parameter type
NO_INT_STRING=Not a string
TO_LIMIT=Limit exceeded
PASSWORD_MIN_LENGTH_FAIL=Insufficient password length, minimum length:%s
PASSWORD_MAX_LENGTH_FAIL=Password length limit exceeded, maximum length:%
PASSWORD_IS_EMPTY=Password cannot be empty
TIME_IS_NOT_FULL=The total time of these periods is less than 24 hours
DATE_IS_NOT_FULL=Dates are not consecutive
START_GE_END_TIME=Start time cannot be later than end time
CONNECT_SUCCESS=Connection successful
CONNECT_FAILED=Connection failed
DATA_REPEAT=Data already exists or is duplicate
DATETIME_IS_NULL=Date or time parameter is null
FAIL_CONNECT_GOCONTROL=Communication with controller failed, operation not successful
ILLEGAL_ID_NULL=ID cannot be empty
USER_PASSWORD_INVALID=Wrong username or password
SOC_EXCEED=SOCs cannot exceed 100
IP_ILLEGAL=IP format error
ROLE_REPEAT=Role name already exists
AD_URL_INVALID=Failed to connect to the AD authentication server
POWER_FACTOR_CONTROL_VALUE_EXCEED=The power factor control value must be between 0.9 - 1
AREA_EXISTS=The area corresponding to this configuration already exists
ACCESS_LIMIT_OVER_COUNT=access times over the limit count
ACCESS_LIMIT_SERVER_ERROR=error happened in server when access limit
CODE_IS_INCORRECT=incorrect verification code
PHONE_IS_NOT_REGISTER=phone number is not register
CODE_IS_EXPIRE=code is expire or invalid
SMS_SEND_ONE_MINUTE=sms code can be sent only once a minute
PHONE_ILLEGAL=not a standard phone number
PASSWORD_INCONSISTENT=the two passwords are inconsistent
PHONE_IS_REGISTER=phone number is already registered
PASSWORD_FORMAT=The password must be 6-16 characters in length and cannot contain special characters
USERNAME_FORMAT=The username must be 5-24 characters in length and cannot contain special characters
#for user log
EVENT=event manage
EVENT_CONFIRM=confirm fault
EVENT_EMS_HIDE=Add fault masking
EVENT_ALARM_SWITCH=New alarm switch
METER=meter manage
METER_ADD=add meter
METER_DEL=delete meter
METER_UPDATE=update meter
CONTROL=control manage
CONTROL_ADD=add control
CONTROL_UPDATE=update control
URL_ADD=add project url
DEVICE=device manage
DEVICE_ADD=add device
DEVICE_UPDATE=update device
DEVICE_DEL=delete device
GROUP=group manage
GROUP_ADD=add group
GROUP_UPDATE=update group
GROUP_UPDATE_EARNINGS=Modify group revenue
GROUP_DEL=delete group
OPERATION=operation profit
OPERATION_PRICE_SAVE=save price
MANAGE_AREA=supervise area manage
MANAGE_AREA_ADD=add area
MANAGE_AREA_UPDATE=update area
MANAGE_AREA_DEL=delete area
MANAGE_PRICE=supervise price manage
MANAGE_PRICE_SAVE=save price
MANAGE_PROJECT=supervise project manage
MANAGE_PROJECT_ADD=add project
MANAGE_PROJECT_UPDATE=update project
MANAGE_PROJECT_ASSIGN=assign project
MANAGE_PROJECT_DEL=delete project
MANAGE_ROLE=supervise role manage
MANAGE_ROLE_ADD=add role
MANAGE_ROLE_UPDATE=update role
MANAGE_ROLE_DEL=delete role
MANAGE_USER=supervise user manage
MANAGE_USER_CH_PWD=update password
MANAGE_USER_CH_OTHER_PWD=update other password
MANAGE_USER_UPDATE=update user
MANAGE_USER_ADD=add user
MANAGE_USER_DEL=delete user
MANAGE_USER_CH_OTHER_PHONE=update other phone
OAUTH=oauth
OAUTH_LOGIN=login
OAUTH_LOGOUT=logout
PHONE_CODE=phone code
PHONE_CODE_SEND=send phone code
PHONE_CODE_VERIFY=verify phone code
GRAPHIC_CODE=graphic code
GRAPHIC_CODE_SEND=send graphic code
GRAPHIC_CODE_VERIFY=verify graphic code
STRATEGY=strategy manage
STRATEGY_TIME_ADD=add time
STRATEGY_TIME_UPDATE=update time
STRATEGY_TIME_DEL=delete time
STRATEGY_GROUP_UPDATE=update group strategy
STRATEGY_TIME_IMPORT=import time
STRATEGY_GROUP_IMPORT=import group strategy
STRATEGY_UPLOAD=upload strategy
SYSTEM=system config
SYSTEM_SWITCH=switch on/off system
ROLE=role manage
ROLE_ADD=add role
ROLE_UPDATE=update role
ROLE_DEL=delete role
ROLE_AUTHOR_CANCEL=Revoke authorization
ROLE_AUTHOR=Authorization role
USER=user manage
USER_CH_PWD=update password
USER_CH_PHONE=update phone
SELF=visitor
SELF_REGISTER=register
SELF_CANCEL=register cancel
SELF_CANCEL_STOP=register cancel cancellation
USER_MANAGE=user manage
USER_MANAGE_CH_OTHER_PWD=update other password
USER_MANAGE_UPDATE=upate user
USER_MANAGE_ADD=add user
USER_MANAGE_DEL=delete user
USER_MANAGE_CH_OTHER_PHONES=update other phone
VERSION=version manage
VERSION_ADD=add version
VERSION_DEL=delete version
VERSION_UPDATE=update version
USER_CH_PWD_PHONE=update user password by phone
MANAGE_PROJECT_URL_ADD=add project url
DEVICE_DISCONNECT_GRID=Switching Grid Connection
PREDICTION_CONFIG_EXISTS=prediction config is exist
DEVICE_MAINTAIN=device maintain
CONTROLLABLE=controllable manage
CONTROLLABLE_ADD=add controllable
CONTROLLABLE_UPDATE=update controllable
CONTROLLABLE_DEL=delete controllable
SYSTEM_REST=device rest
INDEX_EXIST=index already exists
NOTICE=notice manage
NOTICE_ADD=add notice
NOTICE_UPDATE=update notice
NOTICE_DEL=delete notice
REMEDIES=remedies manage
REMEDIES_DATA_ADD=add remedies data
REMEDIES_DATA_UPDATE=update remedies data
REMEDIES_DATA_DEL=delete remedies data
EVENT_DELETE=event message delete
ILLEGAL_EMAIL=invalid email
EMAIL_IS_NOT_REGISTER=email is not register
MANAGE_USER_CH_OTHER_EMAIL=update other user email
USER_CH_EMAIL=update email
USER_CH_PWD_EMAIL=update user password by email
FAVOURITE_PROJECT_ADDED=favourite project already added
CLOUD_LARGESCREEN_NOT_OPEN=cloud screen is closed
Demand_Email_ADD=demand email add
Demand_Email=demand email manage
Demand_Email_DEL=demand email delete
Demand_Email_UPDATE=demand email update
EMAIL_IS_REGISTER=email is already registered
CAMERA=camera manage
CAMERA_ADD=add camera
CAMERA_UPDATE=update camera
CAMERA_DEL=delete camera
PRICE_TEMPLATE=Electricity Price Configuration Template
PRICE_TEMPLATE_DEL=delete price template
PRICE_TEMPLATE_SAVE_UPDATE=save or update price template
INCOME_DIVIDE=Revenue sharing
INCOME_DIVIDE_ADD=Increase revenue sharing
INCOME_DIVIDE_UPDATE=Update revenue sharing
RERUN=rerun cache data
RERUN_BATTERY=rerun battery cache data
RERUN_PV=rerun pv cache data
RERUN_WIND=rerun wind cache data
RERUN_DEMAND=rerun demand cache data
RERUN_WASTER=Recalculate waste heat data
RERUN_DAYREPORT=Regenerate daily report
INVESTOR=investor manage
INVESTOR_ADD=add investor
INVESTOR_UPDATE=update investor
INVESTOR_DEL=delete investor
IP_PORT_EXIST=ip and port already exists
CONTROLLABLE_SOCKET_CAN_PARAMS=SocketCAN
REMEDIES_WHOLE_STATION=Whole-station data replenishment
DAILY_REMEDIES=Daily report replenishment
MONTH_REMEDIES=Monthly report replenishment
YEAR_REMEDIES=Annual report replenishment
REMEDIES_METER=Electric meter data replenishment
PROJECT=Project Homepage
ALARM_UPDATE=Modify Alarm Status
DATA_CALIBRATION=Data calibration
WIND=Wind power generation
WASTER=Waste heat power generation
EMS=Battery revenue
PV=Photovoltaic power generation
CONFIGURATION=Project Configuration
CONFIGURATION_ADD=Add Project Configuration
CONFIGURATION_UPDATE=Edit Project Configuration
CONFIGURATION_DEL=Delete Project Configuration
MAINTENANCE=Operation and Maintenance Information
MAINTENANCE_UPDATE=Update Operation and Maintenance Information
CHAT_PROMPT=You are an energy industry expert and should answer based on the following rules: \
1. Only answer questions related to energy storage technology, photovoltaic power generation, electricity, new energy, Internet of things and other fields, and refuse to respond to other fields \
2. Use the user's language to answer the question, concise and professional language but avoid academic jargon \
3. The referenceable basic information data of the current project is as follows: %s\
4. The referenceable operational data of the current project is as follows: %s\
5. In the reference data, the unit of quantity is kwh, the unit of power is kw, and the unit of benefit and cost is ￥\
6. The reference data for energy storage includes the following fields:total_charge_quantity、total_discharge_quantity、total_discharge_benefit、total_charge_cost\
7. In the reference data, "electric_benefit" is defined as ESS Revenue, "total_charge_quantity" as ESS Charge, and "total_discharge_quantity" as ESS Discharge\
8. 1,000 kWh = 1 MWh, 1,000 MWh = 1 GWh. Display energy in MWh when exceeding 1,000 kWh, and in GWh when exceeding 1,000 MWh \
9. Reference: createTime in the data is interpreted as commissioning time, timezone data is used when converting dates
STRATEGY_TEMPLATE=strategy template manage
STRATEGY_TEMPLATE_ITEM_ADD=strategy template item add
STRATEGY_TEMPLATE_ITEM_UPDATE=strategy template item update
STRATEGY_TEMPLATE_ITEM_DEL=strategy template item delete
STRATEGY_TEMPLATE_IMPORT_MONTH=strategy template import with month
STRATEGY_TEMPLATE_IMPORT_DAYS=strategy template import with days
Capacity_Email_ADD=capacity email add
Capacity_Email=capacity email manage
Capacity_Email_DEL=capacity email delete
Capacity_Email_UPDATE=capacity email update
ALARM_NO_MATCH_NOTIFICATION=No matching notification found
ALARM_CONFIG=告警配置
ALARM_CONFIG_ADD_OR_UPDATE=告警配置新增或更新
ALARM_NOTIFICATION=告警通知
ALARM_NOTIFICATION_ADD=告警通知新增
ALARM_NOTIFICATION_UPDATE=告警通知更新
ALARM_NOTIFICATION_DEL=告警通知删除