package com.wifochina.modules.alarm.controller;

import com.wifochina.common.constants.AlarmConstants;
import com.wifochina.common.page.Result;
import com.wifochina.modules.alarm.entity.AlarmConfigEntity;
import com.wifochina.modules.alarm.entity.AlarmNotificationEntity;
import com.wifochina.modules.alarm.request.AlarmNotificationRequest;
import com.wifochina.modules.alarm.request.BatchAlarmNotificationRequest;
import com.wifochina.modules.alarm.request.BatchDeleteAlarmNotificationRequest;
import com.wifochina.modules.alarm.request.BatchEditAlarmNotificationRequest;
import com.wifochina.modules.alarm.service.AlarmConfigService;
import com.wifochina.modules.alarm.service.AlarmNotificationService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;

import org.springframework.beans.BeanUtils;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import javax.annotation.Resource;

/**
 * 告警通知配置 前端控制器
 *
 * <AUTHOR>
 * @since 2025-07-30
 */
@RestController
@RequestMapping("/alarm/notification")
@Api(value = "alarm-notification", tags = "告警通知配置管理")
public class AlarmNotificationController {

    @Resource private AlarmNotificationService alarmNotificationService;
    @Resource private AlarmConfigService alarmConfigService;
    @Resource private StringRedisTemplate stringRedisTemplate;

    /** 根据告警ID查询通知配置列表 */
    @GetMapping("/list")
    @ApiOperation("根据告警ID查询通知配置列表")
    @PreAuthorize("hasAuthority('/system/showAlarmSwitch')")
    public Result<List<AlarmNotificationEntity>> list(
            @ApiParam(value = "告警ID", required = true) @RequestParam("alarmId") String alarmId) {
        if (alarmId == null || alarmId.isEmpty()) {
            return Result.failure("400", "告警ID不能为空", null);
        }
        List<AlarmNotificationEntity> list = alarmNotificationService.getByAlarmId(alarmId);
        return Result.success(list);
    }

    /** 新增告警通知配置 */
    @PostMapping("/add")
    @ApiOperation("新增告警通知配置")
    @PreAuthorize("hasAuthority('/system/operateAlarmSwitch')")
    public Result<Object> add(@RequestBody AlarmNotificationRequest request) {
        if (request.getAlarmId() == null) {
            return Result.failure("400", "告警ID不能为空", null);
        }
        AlarmNotificationEntity alarmNotification = new AlarmNotificationEntity();
        BeanUtils.copyProperties(request, alarmNotification);
        alarmNotificationService.addAlarmNotification(alarmNotification);
        // 清除缓存
        AlarmConfigEntity alarmConfig = alarmConfigService.getById(alarmNotification.getAlarmId());
        clearCache(alarmConfig.getProjectId());
        return Result.success();
    }

    /** 更新告警通知配置 */
    @PostMapping("/update")
    @ApiOperation("更新告警通知配置")
    @PreAuthorize("hasAuthority('/system/operateAlarmSwitch')")
    public Result<Object> update(@RequestBody AlarmNotificationRequest request) {
        if (request.getId() == null) {
            return Result.failure("400", "ID不能为空", null);
        }
        AlarmNotificationEntity alarmNotification = new AlarmNotificationEntity();
        BeanUtils.copyProperties(request, alarmNotification);
        alarmNotificationService.updateAlarmNotification(alarmNotification);
        // 清除缓存
        AlarmConfigEntity alarmConfig = alarmConfigService.getById(alarmNotification.getAlarmId());
        clearCache(alarmConfig.getProjectId());
        return Result.success();
    }

    /** 删除告警通知配置 */
    @PostMapping("/delete/{id}")
    @ApiOperation("删除告警通知配置")
    @PreAuthorize("hasAuthority('/system/operateAlarmSwitch')")
    public Result<Object> delete(@PathVariable("id") String id) {
        if (id == null || id.isEmpty()) {
            return Result.failure("400", "ID不能为空", null);
        }
        alarmNotificationService.deleteAlarmNotification(id);
        return Result.success();
    }

    /** 批量新增告警通知配置 */
    @PostMapping("/batch/add")
    @ApiOperation("批量新增告警通知配置")
    @PreAuthorize("hasAuthority('/manage/function/alarm/add')")
    public Result<Object> batchAddAlarmNotification(
            @RequestBody BatchAlarmNotificationRequest request) {
        Assert.notEmpty(request.getProjectIds(), "项目ID列表不能为空");
        alarmNotificationService.batchAddAlarmNotification(request);
        return Result.success();
    }

    /** 批量编辑告警通知配置 */
    @PostMapping("/batch/edit")
    @ApiOperation("批量编辑告警通知配置")
    @PreAuthorize("hasAuthority('/manage/function/alarm/edit')")
    public Result<Object> batchEditAlarmNotification(
            @RequestBody BatchEditAlarmNotificationRequest request) {
        Assert.notEmpty(request.getProjectIds(), "项目ID列表不能为空");
        alarmNotificationService.batchEditAlarmNotification(request);
        return Result.success();
    }

    /** 批量删除告警通知配置 */
    @PostMapping("/batch/delete")
    @ApiOperation("批量删除告警通知配置")
    @PreAuthorize("hasAuthority('/manage/function/alarm/delete')")
    public Result<Object> batchDeleteAlarmNotification(
            @RequestBody BatchDeleteAlarmNotificationRequest request) {
        Assert.notEmpty(request.getProjectIds(), "项目ID列表不能为空");
        alarmNotificationService.batchDeleteAlarmNotification(request);
        return Result.success();
    }

    /** 清除缓存 */
    public void clearCache(String projectId) {
        String redisKey = AlarmConstants.buildAlarmSwitchRedisKey(projectId);
        stringRedisTemplate.delete(redisKey);
    }
}
