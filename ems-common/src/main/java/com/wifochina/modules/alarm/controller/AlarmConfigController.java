package com.wifochina.modules.alarm.controller;

import com.wifochina.common.log.Log;
import com.wifochina.common.page.Result;
import com.wifochina.modules.alarm.entity.AlarmConfigEntity;
import com.wifochina.modules.alarm.request.AlarmConfigRequest;
import com.wifochina.modules.alarm.service.AlarmConfigService;
import com.wifochina.modules.log.OperationType;
import com.wifochina.modules.oauth.util.WebUtils;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import org.springframework.beans.BeanUtils;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * 告警配置 前端控制器
 *
 * <AUTHOR>
 * @since 2025-07-30
 */
@RestController
@RequestMapping("/alarm/config")
@Api(value = "alarm-config", tags = "告警配置管理")
public class AlarmConfigController {

    @Resource private AlarmConfigService alarmConfigService;

    /** 根据项目ID查询告警配置列表 */
    @GetMapping("/list")
    @ApiOperation("查询告警配置列表")
    @PreAuthorize("hasAuthority('/system/showAlarmSwitch')")
    public Result<List<AlarmConfigEntity>> list() {
        String projectId = WebUtils.projectId.get();
        List<AlarmConfigEntity> list = alarmConfigService.getByProjectId(projectId);
        return Result.success(list);
    }

    /** 开关告警配置 */
    @PostMapping("/switch")
    @ApiOperation("新增或更新告警配置")
    @Log(module = "ALARM", type = OperationType.ADD_OR_UPDATE)
    @PreAuthorize("hasAuthority('/system/operateAlarmSwitch')")
    public Result<Object> switchAlarmConfig(@RequestBody @Valid AlarmConfigRequest request) {
        AlarmConfigEntity alarmConfig = new AlarmConfigEntity();
        BeanUtils.copyProperties(request, alarmConfig);
        alarmConfig.setProjectId(WebUtils.projectId.get());
        alarmConfigService.switchAlarmConfig(alarmConfig);
        return Result.success();
    }
}
