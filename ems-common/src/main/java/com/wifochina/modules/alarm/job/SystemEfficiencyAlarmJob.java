package com.wifochina.modules.alarm.job;

import cn.hutool.core.collection.CollUtil;

import com.wifochina.common.constants.AlarmConstants;
import com.wifochina.common.constants.AlarmContentEnum;
import com.wifochina.common.constants.AlarmLevelEnum;
import com.wifochina.modules.alarm.dtos.AlarmCacheDTO;
import com.wifochina.modules.alarm.entity.AlarmConfigEntity;
import com.wifochina.modules.alarm.service.AlarmHandler;
import com.wifochina.modules.diagram.request.RequestWithGroupId;
import com.wifochina.modules.diagram.service.AbstractDiagramExtendService;
import com.wifochina.modules.project.entity.ProjectEntity;
import com.wifochina.modules.project.service.ProjectService;

import lombok.extern.slf4j.Slf4j;

import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.springframework.scheduling.quartz.QuartzJobBean;
import org.springframework.stereotype.Component;

import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Stream;

import javax.annotation.Resource;

/**
 * 系统效率告警任务
 *
 * <AUTHOR>
 * @since 2025-08-19
 */
@Slf4j
@Component
public class SystemEfficiencyAlarmJob extends QuartzJobBean {

    @Resource private ProjectService projectService;
    @Resource private AlarmHandler alarmHandler;
    @Resource private AbstractDiagramExtendService diagramExtendService;

    @Override
    public void executeInternal(JobExecutionContext context) throws JobExecutionException {
        String timezone = context.getJobDetail().getJobDataMap().getString("timezone");
        log.info("开始执行系统效率告警任务，时区：{}", timezone);

        try {
            // 查询该时区下所有项目
            List<ProjectEntity> projects = projectService.getProjectsByTimeZone(timezone);
            log.info("时区 {} 下共有 {} 个项目", timezone, projects.size());

            // 遍历项目列表，计算每个项目前一日的系统效率
            for (ProjectEntity project : projects) {
                try {
                    processProjectEfficiencyAlarm(project, timezone);
                } catch (Exception e) {
                    log.error("处理项目 {} 的系统效率告警时发生异常", project.getProjectName(), e);
                }
            }

            log.info("系统效率告警任务执行完成，时区：{}", timezone);
        } catch (Exception e) {
            log.error("执行系统效率告警任务时发生异常，时区：{}", timezone, e);
            throw new JobExecutionException(e);
        }
    }

    /**
     * 处理单个项目的系统效率告警
     *
     * @param project 项目实体
     * @param timezone 时区
     */
    private void processProjectEfficiencyAlarm(ProjectEntity project, String timezone) {
        log.debug("开始处理项目 {} 的系统效率告警", project.getProjectName());

        // 查询项目的系统效率告警配置
        // 查询当前项目告警配置
        AlarmCacheDTO alarmCache = alarmHandler.cacheAlarmInfo(project.getId());
        AlarmConfigEntity alarmConfig =
                Optional.ofNullable(alarmCache)
                        .map(AlarmCacheDTO::getAlarmConfigList)
                        .map(List::stream)
                        .map(
                                i ->
                                        i.filter(
                                                j ->
                                                        AlarmContentEnum.SYSTEM_EFFICIENCY.getCode()
                                                                == j.getAlarmContent()))
                        .flatMap(Stream::findFirst)
                        .orElse(null);
        if (alarmConfig == null || !alarmConfig.getIsEnabled()) {
            log.debug("项目 {} 未配置系统效率告警或已禁用", project.getProjectName());
            return;
        }

        Float efficiencyThreshold = alarmConfig.getEfficiencyReminderThreshold();
        if (efficiencyThreshold == null) {
            log.warn("项目 {} 的系统效率告警阈值未配置", project.getProjectName());
            return;
        }

        // 计算前一日的系统效率
        double systemEfficiency = calculateSystemEfficiency(project, timezone);
        log.debug(
                "项目 {} 前一日系统效率：{}%，阈值：{}%",
                project.getProjectName(), systemEfficiency, efficiencyThreshold);

        // 判断是否需要告警
        if (systemEfficiency < efficiencyThreshold) {
            log.warn(
                    "项目 {} 系统效率 {}% 低于阈值 {}%，触发告警",
                    project.getProjectName(), systemEfficiency, efficiencyThreshold);
            Map<String, String> extLableMap =
                    Map.of(
                            AlarmConstants.LABLE_DEVIATION,
                            String.valueOf(efficiencyThreshold),
                            AlarmConstants.LABLE_VALUE,
                            String.valueOf(systemEfficiency));
            // 发送告警
            alarmHandler.handleAlarm(
                    project,
                    AlarmContentEnum.SYSTEM_EFFICIENCY,
                    AlarmLevelEnum.warning,
                    extLableMap);
        }
    }

    /**
     * 计算项目前一日的系统效率
     *
     * @param project 项目实体
     * @param timezone 时区
     * @return 系统效率百分比
     */
    private double calculateSystemEfficiency(ProjectEntity project, String timezone) {
        try {
            // 获取前一日的日期范围
            LocalDate yesterday = LocalDate.now(ZoneId.of(timezone)).minusDays(1);
            long startTime = yesterday.atStartOfDay(ZoneId.of(timezone)).toEpochSecond();
            long endTime = yesterday.plusDays(1).atStartOfDay(ZoneId.of(timezone)).toEpochSecond();

            log.debug(
                    "计算项目 {} 在 {} 的系统效率，时间范围：{} - {}",
                    project.getProjectName(),
                    yesterday,
                    Instant.ofEpochSecond(startTime),
                    Instant.ofEpochSecond(endTime));

            // 计算昨日效率
            Map<Long, Double> efficiencyRate =
                    diagramExtendService.getEfficiencyRate(
                            new RequestWithGroupId()
                                    .setStartDate(startTime)
                                    .setEndDate(endTime)
                                    .setGroupId("all")
                                    .setProjectId(project.getId()));
            if (CollUtil.isEmpty(efficiencyRate)) {
                log.error("计算项目 {} 系统效率返回空，不做处理", project.getProjectName());
            }
            return efficiencyRate.get(startTime);

        } catch (Exception e) {
            log.error("计算项目 {} 系统效率时发生异常", project.getProjectName(), e);
            return 100.0; // 异常时返回100%，避免误报
        }
    }
}
