package com.wifochina.modules.alarm.job;

import com.wifochina.modules.income.job.CustomTimezoneQuartzService;

import lombok.extern.slf4j.Slf4j;

import org.quartz.*;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;
import java.util.TimeZone;

import javax.annotation.Resource;

/**
 * 系统效率告警任务Quartz服务实现类
 * 参考 DayReportJobQuartzServiceImpl 实现
 *
 * <AUTHOR>
 * @since 2025-08-19
 */
@Slf4j
@Service
public class SystemEfficiencyAlarmJobQuartzServiceImpl implements CustomTimezoneQuartzService {

    private static final String JOB_NAME = "system-efficiency-alarm-job:%s";
    private static final String JOB_TRIGGER_NAME = "system-efficiency-alarm-trigger:%s";
    private static final String JOB_GROUP = "system-efficiency-alarm-job";

    @Resource protected Scheduler scheduler;

    /**
     * 立即启动测试方法
     *
     * @param timezone 时区
     */
    public void startNowTest(String timezone) {
        JobDetail jobDetail = getJobDetail(timezone);
        Trigger trigger =
                TriggerBuilder.newTrigger()
                        .withIdentity("triggerName", JOB_GROUP)
                        .startNow()
                        .build();
        try {
            scheduler.scheduleJob(jobDetail, trigger);
        } catch (SchedulerException e) {
            log.error("立即启动系统效率告警任务失败，时区：{}", timezone, e);
            throw new RuntimeException(e);
        }
    }

    @Override
    public void addJob(String timezone) {
        log.info("开始为时区 {} 添加系统效率告警任务", timezone);

        try {
            // 构建JobDetail (表示一个具体的可执行的调度程序，Job是这个可执行调度程序所要执行的内容)
            JobDetail jobDetail = getJobDetail(timezone);
            
            // 构建触发器Trigger (调度参数的配置，代表何时触发该任务)
            Trigger trigger = getTrigger(timezone, getJobCrontab());
            
            if (trigger != null) {
                // 检查任务是否已存在
                String jobName = String.format(JOB_NAME, timezone);
                if (scheduler.checkExists(JobKey.jobKey(jobName, JOB_GROUP))) {
                    log.warn("时区 {} 的系统效率告警任务已存在，跳过添加", timezone);
                    return;
                }

                scheduler.scheduleJob(jobDetail, trigger);
                
                if (!scheduler.isStarted()) {
                    scheduler.start();
                }
                
                log.info("时区 {} 的系统效率告警任务添加成功", timezone);
            } else {
                log.error("为时区 {} 创建触发器失败", timezone);
            }
        } catch (SchedulerException e) {
            log.error("为时区 {} 添加系统效率告警任务失败", timezone, e);
        }
    }

    /**
     * 创建触发器
     *
     * @param timezone 时区
     * @param jobCron cron表达式
     * @return 触发器
     */
    public Trigger getTrigger(String timezone, String jobCron) {
        String triggerName = String.format(JOB_TRIGGER_NAME, timezone);
        return TriggerBuilder.newTrigger()
                .withIdentity(triggerName, JOB_GROUP)
                .withSchedule(
                        CronScheduleBuilder.cronSchedule(jobCron)
                                .inTimeZone(TimeZone.getTimeZone(timezone)))
                .build();
    }

    /**
     * 创建任务详情
     *
     * @param timezone 时区
     * @return 任务详情
     */
    public JobDetail getJobDetail(String timezone) {
        Map<String, String> jobData = new HashMap<>(1);
        String jobName = String.format(JOB_NAME, timezone);
        jobData.put("timezone", timezone);
        
        JobDataMap jobDataMap = new JobDataMap();
        jobDataMap.putAll(jobData);
        
        return JobBuilder.newJob(SystemEfficiencyAlarmJob.class)
                .withIdentity(jobName, JOB_GROUP)
                .usingJobData(jobDataMap)
                .storeDurably()
                .build();
    }

    /**
     * 获取任务的cron表达式
     * 每日凌晨2点整执行
     *
     * @return cron表达式
     */
    public String getJobCrontab() {
        return "0 0 2 * * ?";
    }

    /**
     * 删除指定时区的任务
     *
     * @param timezone 时区
     */
    public void removeJob(String timezone) {
        try {
            String jobName = String.format(JOB_NAME, timezone);
            String triggerName = String.format(JOB_TRIGGER_NAME, timezone);
            
            // 删除触发器
            scheduler.unscheduleJob(TriggerKey.triggerKey(triggerName, JOB_GROUP));
            
            // 删除任务
            scheduler.deleteJob(JobKey.jobKey(jobName, JOB_GROUP));
            
            log.info("时区 {} 的系统效率告警任务删除成功", timezone);
        } catch (SchedulerException e) {
            log.error("删除时区 {} 的系统效率告警任务失败", timezone, e);
        }
    }

    /**
     * 检查指定时区的任务是否存在
     *
     * @param timezone 时区
     * @return 是否存在
     */
    public boolean jobExists(String timezone) {
        try {
            String jobName = String.format(JOB_NAME, timezone);
            return scheduler.checkExists(JobKey.jobKey(jobName, JOB_GROUP));
        } catch (SchedulerException e) {
            log.error("检查时区 {} 的系统效率告警任务是否存在时发生异常", timezone, e);
            return false;
        }
    }
}
