package com.wifochina.modules.alarm.job;

import com.wifochina.modules.project.entity.ProjectEntity;
import com.wifochina.modules.project.service.ProjectService;

import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.CommandLineRunner;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/**
 * 系统效率告警任务启动类
 * 参考 DayReportJobStart 实现
 *
 * <AUTHOR>
 * @since 2025-08-19
 */
@Slf4j
@Component
public class SystemEfficiencyAlarmJobStart implements CommandLineRunner {

    @Resource private ProjectService projectService;
    @Resource private SystemEfficiencyAlarmJobQuartzServiceImpl systemEfficiencyAlarmJobQuartzService;
    @Resource private ThreadPoolTaskExecutor threadPoolTaskExecutor;

    @Override
    public void run(String... args) {
        log.info("开始启动系统效率告警任务");

        try {
            // 查询所有未删除的项目
            List<ProjectEntity> projectEntities =
                    projectService
                            .lambdaQuery()
                            .eq(ProjectEntity::getWhetherDelete, false)
                            .list();

            // 统计所有项目的时区
            List<String> timezoneList =
                    projectEntities.stream()
                            .map(ProjectEntity::getTimezone)
                            .distinct()
                            .collect(Collectors.toList());

            log.info("共发现 {} 个项目，涉及 {} 个时区：{}", 
                    projectEntities.size(), timezoneList.size(), timezoneList);

            // 为每个时区启动一个定时任务
            for (String timezone : timezoneList) {
                threadPoolTaskExecutor.submit(() -> {
                    try {
                        log.info("为时区 {} 启动系统效率告警任务", timezone);
                        systemEfficiencyAlarmJobQuartzService.addJob(timezone);
                        log.info("时区 {} 的系统效率告警任务启动成功", timezone);
                    } catch (Exception e) {
                        log.error("为时区 {} 启动系统效率告警任务失败", timezone, e);
                    }
                });
            }

            log.info("系统效率告警任务启动完成");
        } catch (Exception e) {
            log.error("启动系统效率告警任务时发生异常", e);
        }
    }
}
