package com.wifochina.modules.alarm.dtos;

import com.wifochina.modules.alarm.entity.AlarmConfigEntity;
import com.wifochina.modules.event.entity.AlarmSwitchEntity;

import com.wifochina.modules.project.entity.ProjectEntity;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 告警开关缓存对象
 *
 * <AUTHOR>
 * @since 2025-07-08
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class AlarmCacheDTO {

    private ProjectEntity projectInfo;

    private List<AlarmConfigEntity> alarmConfigList;

    private Boolean allEmsKernelAlarmEnabled;

    private Boolean allEmsSubDeviceAlarmEnabled;

    private Boolean allMeterAlarmEnabled;

    private List<AlarmSwitchEntity> alarmSwitchList;
}
