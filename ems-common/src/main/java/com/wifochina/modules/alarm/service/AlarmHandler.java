package com.wifochina.modules.alarm.service;

import cn.hutool.core.util.StrUtil;

import com.alibaba.fastjson.JSON;
import com.weihengtech.alert.client.AlertApiClient;
import com.weihengtech.alert.model.base.BaseAlert;
import com.weihengtech.alert.model.dto.AlertCreateDTO;
import com.wifochina.common.constants.AlarmConstants;
import com.wifochina.common.constants.AlarmContentEnum;
import com.wifochina.common.constants.AlarmLevelEnum;
import com.wifochina.modules.alarm.dtos.AlarmCacheDTO;
import com.wifochina.modules.alarm.entity.AlarmConfigEntity;
import com.wifochina.modules.event.service.AlarmSwitchService;
import com.wifochina.modules.project.entity.ProjectEntity;
import com.wifochina.modules.project.service.ProjectService;

import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.time.Instant;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

/**
 * 告警配置 服务类
 *
 * <AUTHOR>
 * @since 2025-07-30
 */
@Component
public class AlarmHandler {

    @Value("${alert.api.app-name}")
    private String appName;

    @Value("${alert.api.monitor}")
    private String monitor;

    @Resource private AlarmConfigService alarmConfigService;
    @Resource private AlarmSwitchService alarmSwitchService;
    @Resource private StringRedisTemplate stringRedisTemplate;
    @Resource private AlertApiClient alertApiClient;
    @Resource private ProjectService projectService;

    /**
     * 缓存告警配置
     *
     * @param projectId 项目id
     * @return AlarmCacheDTO
     */
    public AlarmCacheDTO cacheAlarmInfo(String projectId) {
        String redisKey = AlarmConstants.buildAlarmSwitchRedisKey(projectId);
        if (stringRedisTemplate.hasKey(redisKey)) {
            String cacheStr = stringRedisTemplate.opsForValue().get(redisKey);
            if (StrUtil.isNotBlank(cacheStr)) {
                return JSON.parseObject(cacheStr, AlarmCacheDTO.class);
            }
        }
        AlarmCacheDTO res = AlarmCacheDTO.builder().build();
        List<AlarmConfigEntity> alarmConfigList = alarmConfigService.getByProjectId(projectId);
        if (CollectionUtils.isEmpty(alarmConfigList)) {
            return res;
        }
        res.setAlarmConfigList(alarmConfigList);
        ProjectEntity projectInfo = projectService.getById(projectId);
        res.setProjectInfo(projectInfo);
        AlarmCacheDTO cacheItem = alarmSwitchService.listAlarmSwitch(projectId);
        res.setAllEmsKernelAlarmEnabled(cacheItem.getAllEmsKernelAlarmEnabled());
        res.setAllEmsSubDeviceAlarmEnabled(cacheItem.getAllEmsSubDeviceAlarmEnabled());
        res.setAllMeterAlarmEnabled(cacheItem.getAllMeterAlarmEnabled());
        res.setAlarmSwitchList(cacheItem.getAlarmSwitchList());
        stringRedisTemplate.opsForValue().set(redisKey, JSON.toJSONString(res));
        return res;
    }

    /**
     * 根据配置发送告警通知
     *
     * @param project 项目实体
     * @param alarmContentEnum 告警内容枚举
     * @param alarmLevelEnum 告警级别枚举
     */
    public void handleAlarm(
            ProjectEntity project,
            AlarmContentEnum alarmContentEnum,
            AlarmLevelEnum alarmLevelEnum,
            Map<String, String> extlableMap) {
        AlertCreateDTO param =
                getAlertCreate(project, alarmContentEnum, alarmLevelEnum, extlableMap);
        alertApiClient.sendAlert(param);
    }

    @NotNull
    private AlertCreateDTO getAlertCreate(
            ProjectEntity projectInfo,
            AlarmContentEnum alarmContentEnum,
            AlarmLevelEnum alarmLevelEnum,
            Map<String, String> extlableMap) {
        AlertCreateDTO param = new AlertCreateDTO();
        param.setSource(appName);
        param.setSubGroup(projectInfo.getProjectName());
        BaseAlert baseAlert =
                getBaseAlert(projectInfo, alarmContentEnum, alarmLevelEnum, extlableMap);
        param.setAlerts(Collections.singletonList(baseAlert));
        return param;
    }

    @NotNull
    private BaseAlert getBaseAlert(
            ProjectEntity projectInfo,
            AlarmContentEnum alarmContentEnum,
            AlarmLevelEnum alarmLevelEnum,
            Map<String, String> extlableMap) {
        BaseAlert baseAlert = new BaseAlert();
        baseAlert.setStartsAt(
                DateTimeFormatter.ISO_INSTANT.format(
                        Instant.now().atZone(ZoneId.of(projectInfo.getTimezone()))));
        baseAlert.setStatus(BaseAlert.STATUS_FIRING);
        Map<String, String> lableMap =
                buildBasicLableMap(projectInfo, alarmContentEnum, alarmLevelEnum);
        lableMap.putAll(extlableMap);
        baseAlert.setLabels(lableMap);
        return baseAlert;
    }

    private Map<String, String> buildBasicLableMap(
            ProjectEntity projectInfo,
            AlarmContentEnum alarmContentEnum,
            AlarmLevelEnum alarmLevelEnum) {
        Map<String, String> lableMap = new HashMap<>();
        lableMap.put(AlarmConstants.LABLE_MONITOR, monitor);
        lableMap.put(AlarmConstants.LABLE_PROJECT_NAME, projectInfo.getProjectName());
        lableMap.put(AlarmConstants.LABLE_ALERT_NAME, alarmContentEnum.getName());
        lableMap.put(AlarmConstants.LABLE_LEVEL, alarmLevelEnum.name());
        lableMap.put(
                AlarmConstants.LABLE_OCCUR_TIME,
                ZonedDateTime.now(ZoneId.of(projectInfo.getTimezone()))
                        .format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
        return lableMap;
    }
}
