package com.wifochina.modules.strategy.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.wifochina.modules.BaseEntity;
import com.wifochina.modules.strategy.request.TimeRange;
import com.wifochina.modules.strategytemplate.common.GoTimeSlotTransformAble;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.time.LocalTime;
import java.util.List;

/**
 * BackFlowLimitRequest
 *
 * @since 2025-07-23 16:06:50
 * <AUTHOR>
 * @version 1.0
 */
@Data
@TableName("t_time_sharing_demand")
@ApiModel(value = "分时demandItem请求")
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
public class TimeSharingDemandEntity extends BaseEntity implements GoTimeSlotTransformAble {

    private Long id;

    private String projectId;

    private String name;

    private String groupId;

    @ApiModelProperty(value = "系统主策略id")
    private Integer systemStrategyId;

    //    @TableField(exist = false)
    //    private List<TimeSharingDemandTimeRangeEntity> timeRanges;

    private Long parentId;

    @TableField(exist = false)
    private List<TimeRange> timeRanges;

    @ApiModelProperty(value = "开始时间")
    private LocalTime startTime;

    @ApiModelProperty(value = "结束时间")
    private LocalTime endTime;

    @ApiModelProperty(value = "需量 true 为勾选 false 为未勾选")
    private Boolean demandItemController;

    @ApiModelProperty(value = "月初需量")
    private Double demandMonthControlPower;

    @ApiModelProperty(value = "防逆流最小值")
    private Double demandControlPower;

    @JsonIgnore
    public LocalTime getStartTime() {
        return startTime;
    }

    @JsonIgnore
    public LocalTime getEndTime() {
        return endTime;
    }
}
