package com.wifochina.modules.strategy.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.wifochina.modules.strategy.entity.TimeSharingDemandEntity;
import com.wifochina.modules.strategy.request.TimeSharingDemandRequest;

import java.util.List;

/**
 * 服务类
 *
 * <AUTHOR>
 * @since 2025-07-23 11:22:01
 */
public interface TimeSharingDemandService extends IService<TimeSharingDemandEntity> {

    List<TimeSharingDemandEntity> listHierarchyBy(String projectId, String groupId);

    List<TimeSharingDemandEntity> listPivotBy(String projectId, String groupId);

    List<TimeSharingDemandEntity> listParentBy(String projectId, String groupId);

    void addDemandItem(TimeSharingDemandRequest timeSharingDemandRequest);

    void editDemandItem(TimeSharingDemandRequest timeSharingDemandRequest);

    TimeSharingDemandEntity findNowRunControlItem(
            String projectId, String groupId, Integer strategyId);

    void refresh(
            String projectId,
            String groupId,
            List<TimeSharingDemandRequest> timeSharingDemandRequests);
}
