package com.wifochina.modules.strategy.request;

import io.swagger.annotations.ApiModelProperty;

import lombok.Data;

@Data
public class PredicateStrategyTemplateRequest {

    @ApiModelProperty(value = "策略模版ID(默认是看今天今天)", required = true)
    private Long templateId;

    @ApiModelProperty(value = "不传默认是看今天今天)", required = true)
    private Long date;

    private String projectId;

    @ApiModelProperty(value = "管理端预览的时候需要时区")
    private String timezoneForManage;
}
