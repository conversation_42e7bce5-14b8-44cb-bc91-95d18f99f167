package com.wifochina.modules.strategy.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wifochina.common.constants.ErrorResultCode;
import com.wifochina.common.exception.ServiceException;
import com.wifochina.modules.project.entity.ProjectEntity;
import com.wifochina.modules.project.service.ProjectService;
import com.wifochina.modules.strategy.converter.TimeRangeConverter;
// import com.wifochina.modules.strategy.converter.TimeSharingDemandConverter;
import com.wifochina.modules.strategy.entity.TimeSharingDemandEntity;
import com.wifochina.modules.strategy.mapper.TimeSharingDemandMapper;
import com.wifochina.modules.strategy.request.TimeRange;
import com.wifochina.modules.strategy.request.TimeSharingDemandRequest;

import com.wifochina.modules.strategy.service.TimeSharingDemandService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.time.LocalTime;
import java.util.*;
import java.util.stream.Collectors;

import javax.transaction.Transactional;

/**
 * Created on 2025/7/23 11:22.
 *
 * <AUTHOR>
 */
@Service
@Slf4j
@Transactional(rollbackOn = Exception.class)
@AllArgsConstructor
public class TimeSharingDemandServiceImpl
        extends ServiceImpl<TimeSharingDemandMapper, TimeSharingDemandEntity>
        implements TimeSharingDemandService {

    private final ProjectService projectService;

    /**
     * pivot 平铺的 list
     *
     * @param projectId : projectId
     * @param groupId : groupId
     * @return : list pivot的
     */
    @Override
    public List<TimeSharingDemandEntity> listPivotBy(String projectId, String groupId) {
        List<TimeSharingDemandEntity> superTimeSharingDemandEntities =
                getBaseMapper()
                        .selectList(
                                new LambdaQueryWrapper<TimeSharingDemandEntity>()
                                        .eq(TimeSharingDemandEntity::getProjectId, projectId)
                                        .eq(TimeSharingDemandEntity::getGroupId, groupId)
                                        .isNull(TimeSharingDemandEntity::getParentId));
        Map<Long, TimeSharingDemandEntity> timeSharingDemandEntityMap =
                superTimeSharingDemandEntities.stream()
                        .collect(Collectors.toMap(TimeSharingDemandEntity::getId, v -> v));
        Set<Long> superIds = timeSharingDemandEntityMap.keySet();
        List<TimeSharingDemandEntity> pivots = new ArrayList<>();
        if (CollUtil.isNotEmpty(superIds)) {
            // 根据superIds
            List<TimeSharingDemandEntity> timeSharingDemandTimeRanges =
                    getBaseMapper()
                            .selectList(
                                    new LambdaQueryWrapper<TimeSharingDemandEntity>()
                                            .eq(TimeSharingDemandEntity::getProjectId, projectId)
                                            .eq(TimeSharingDemandEntity::getGroupId, groupId)
                                            .in(TimeSharingDemandEntity::getParentId, superIds));
            Map<Long, List<TimeSharingDemandEntity>> timeRangesGroupMaps =
                    timeSharingDemandTimeRanges.stream()
                            .collect(Collectors.groupingBy(TimeSharingDemandEntity::getParentId));
            timeRangesGroupMaps
                    .values()
                    .forEach(
                            timeRanges -> {
                                timeRanges.forEach(
                                        timeRange -> {
                                            TimeSharingDemandEntity superTimeSharingDemandEntity =
                                                    timeSharingDemandEntityMap.get(
                                                            timeRange.getParentId());
                                            if (superTimeSharingDemandEntity != null) {
                                                TimeSharingDemandEntity pivot =
                                                        new TimeSharingDemandEntity();
                                                BeanUtils.copyProperties(
                                                        superTimeSharingDemandEntity, pivot);
                                                //
                                                // TimeSharingDemandEntity pivot =
                                                //
                                                //      TimeSharingDemandConverter.INSTANCE.copy(
                                                //
                                                //              superTimeSharingDemandEntity);
                                                pivot.setParentId(
                                                        superTimeSharingDemandEntity.getId());
                                                pivot.setStartTime(timeRange.getStartTime());
                                                pivot.setEndTime(timeRange.getEndTime());
                                                pivot.setId(timeRange.getId());
                                                pivots.add(pivot);
                                            }
                                        });
                            });
        }
        return pivots;
    }

    @Override
    public List<TimeSharingDemandEntity> listParentBy(String projectId, String groupId) {
        return getBaseMapper()
                .selectList(
                        new LambdaQueryWrapper<TimeSharingDemandEntity>()
                                .eq(TimeSharingDemandEntity::getProjectId, projectId)
                                .eq(TimeSharingDemandEntity::getGroupId, groupId)
                                .isNull(TimeSharingDemandEntity::getParentId));
    }

    @Override
    public List<TimeSharingDemandEntity> listHierarchyBy(String projectId, String groupId) {
        List<TimeSharingDemandEntity> superTimeSharingDemandEntities =
                getBaseMapper()
                        .selectList(
                                new LambdaQueryWrapper<TimeSharingDemandEntity>()
                                        .eq(TimeSharingDemandEntity::getProjectId, projectId)
                                        .eq(TimeSharingDemandEntity::getGroupId, groupId)
                                        .isNull(TimeSharingDemandEntity::getParentId));
        List<Long> superIds =
                superTimeSharingDemandEntities.stream()
                        .map(TimeSharingDemandEntity::getId)
                        .collect(Collectors.toList());
        if (CollUtil.isNotEmpty(superIds)) {
            // 根据superIds
            List<TimeSharingDemandEntity> timeSharingDemandTimeRanges =
                    getBaseMapper()
                            .selectList(
                                    new LambdaQueryWrapper<TimeSharingDemandEntity>()
                                            .eq(TimeSharingDemandEntity::getProjectId, projectId)
                                            .eq(TimeSharingDemandEntity::getGroupId, groupId)
                                            .in(TimeSharingDemandEntity::getParentId, superIds));
            Map<Long, List<TimeSharingDemandEntity>> timeRangesGroupMaps =
                    timeSharingDemandTimeRanges.stream()
                            .collect(Collectors.groupingBy(TimeSharingDemandEntity::getParentId));
            superTimeSharingDemandEntities.forEach(
                    superTimeSharingDemandEntity -> {
                        List<TimeSharingDemandEntity> timeSharingTimeRanges =
                                timeRangesGroupMaps.get(superTimeSharingDemandEntity.getId());
                        if (CollUtil.isNotEmpty(timeSharingTimeRanges)) {
                            superTimeSharingDemandEntity.setTimeRanges(
                                    timeSharingTimeRanges.stream()
                                            .map(TimeRangeConverter.INSTANCE::timeSharing2Vo)
                                            .collect(Collectors.toList()));
                        }
                    });
        }
        return superTimeSharingDemandEntities;
    }

    @Override
    public void addDemandItem(TimeSharingDemandRequest timeSharingDemandRequest) {
        // 必须要有对应的 timeRange
        if (CollUtil.isNotEmpty(timeSharingDemandRequest.getTimeRanges())) {
            // add
            TimeSharingDemandEntity timeSharingDemandEntity = new TimeSharingDemandEntity();
            BeanUtils.copyProperties(timeSharingDemandRequest, timeSharingDemandEntity);
            timeSharingDemandEntity.setId(null);
            timeSharingDemandEntity.setParentId(null);
            getBaseMapper().insert(timeSharingDemandEntity);
            // 处理 timeRange
            List<TimeSharingDemandEntity> timeRangeEntityList =
                    getTimeSharingDemandTimeRangeEntities(
                            timeSharingDemandRequest, timeSharingDemandEntity);
            this.saveBatch(timeRangeEntityList);
        } else {
            throw new ServiceException(ErrorResultCode.ILLEGAL_ARGUMENT.value());
        }
    }

    public void validateNoOverlap(List<TimeRange> timeRanges) {
        // 按开始时间排序
        timeRanges.sort(Comparator.comparing(TimeRange::getStartTime));
        for (int i = 0; i < timeRanges.size() - 1; i++) {
            TimeRange current = timeRanges.get(i);
            TimeRange next = timeRanges.get(i + 1);

            // 判断是否重叠：下一个区间的开始时间 < 当前区间的结束时间
            if (next.getStartTime().isBefore(current.getEndTime())) {
                throw new ServiceException(ErrorResultCode.TIME_OVERLAPPED.value());
            }
        }
    }

    /** check time over */
    @Deprecated
    public void checkTime(TimeSharingDemandRequest timeSharingDemandRequest) {
        List<TimeRange> timeRanges = timeSharingDemandRequest.getTimeRanges();
        for (TimeRange timeRange : timeRanges) {
            if (!timeRange.getEndTime().equals(LocalTime.MIDNIGHT)
                    && timeRange.getStartTime().isAfter(timeRange.getEndTime())) {
                throw new ServiceException(ErrorResultCode.TIME_OVERLAPPED.value());
            }
            if (!timeRange.getStartTime().equals(LocalTime.MIDNIGHT)
                    || !timeRange.getEndTime().equals(LocalTime.MIDNIGHT)) {
                if (timeRange.getStartTime().getHour() == timeRange.getEndTime().getHour()
                        && timeRange.getStartTime().getMinute()
                                == timeRange.getEndTime().getMinute()) {
                    throw new ServiceException(ErrorResultCode.TIME_OVERLAPPED.value());
                }
            }
        }
    }

    @NotNull
    private static List<TimeSharingDemandEntity> getTimeSharingDemandTimeRangeEntities(
            TimeSharingDemandRequest timeSharingDemandRequest,
            TimeSharingDemandEntity timeSharingDemandEntity) {
        List<TimeSharingDemandEntity> timeRangeEntityList = new ArrayList<>();
        List<TimeRange> timeRanges = timeSharingDemandRequest.getTimeRanges();
        timeRanges.forEach(
                timeRange -> {
                    TimeSharingDemandEntity demandTimeRange = new TimeSharingDemandEntity();
                    demandTimeRange.setStartTime(timeRange.getStartTime());
                    demandTimeRange.setEndTime(timeRange.getEndTime());
                    demandTimeRange.setParentId(timeSharingDemandEntity.getId());
                    demandTimeRange.setProjectId(timeSharingDemandEntity.getProjectId());
                    demandTimeRange.setGroupId(timeSharingDemandEntity.getGroupId());
                    demandTimeRange.setName(timeSharingDemandEntity.getName());
                    demandTimeRange.setDemandControlPower(
                            timeSharingDemandEntity.getDemandControlPower());
                    demandTimeRange.setDemandMonthControlPower(
                            timeSharingDemandEntity.getDemandMonthControlPower());
                    demandTimeRange.setDemandItemController(
                            timeSharingDemandEntity.getDemandItemController());
                    demandTimeRange.setSystemStrategyId(
                            timeSharingDemandEntity.getSystemStrategyId());
                    timeRangeEntityList.add(demandTimeRange);
                });
        return timeRangeEntityList;
    }

    @Override
    public void editDemandItem(TimeSharingDemandRequest timeSharingDemandRequest) {
        // edit
        TimeSharingDemandEntity timeSharingDemandEntity = new TimeSharingDemandEntity();
        BeanUtils.copyProperties(timeSharingDemandRequest, timeSharingDemandEntity);
        getBaseMapper().updateById(timeSharingDemandEntity);
    }

    @Override
    public TimeSharingDemandEntity findNowRunControlItem(
            String projectId, String groupId, Integer strategyId) {
        LambdaQueryWrapper<TimeSharingDemandEntity> query = Wrappers.lambdaQuery();
        query.eq(TimeSharingDemandEntity::getProjectId, projectId)
                .eq(TimeSharingDemandEntity::getGroupId, groupId)
                .eq(TimeSharingDemandEntity::getSystemStrategyId, strategyId)
                .apply("start_time <= CURTIME()")
                .apply("end_time > CURTIME()");
        return getBaseMapper().selectOne(query);
    }

    /**
     * 1.4.4 added refresh timesharing demand
     *
     * @param projectId : projectId
     * @param groupId : groupId
     * @param timeSharingDemandRequests : list
     */
    @Override
    public void refresh(
            String projectId,
            String groupId,
            List<TimeSharingDemandRequest> timeSharingDemandRequests) {
        this.remove(
                new LambdaQueryWrapper<TimeSharingDemandEntity>()
                        .eq(TimeSharingDemandEntity::getProjectId, projectId)
                        .eq(TimeSharingDemandEntity::getGroupId, groupId));

        if (CollUtil.isNotEmpty(timeSharingDemandRequests)) {
            List<TimeRange> timeRanges = new ArrayList<>();
            timeSharingDemandRequests.forEach(
                    timeSharingDemandRequest -> {
                        timeRanges.addAll(timeSharingDemandRequest.getTimeRanges());
                    });
            if (CollUtil.isNotEmpty(timeRanges)) {
                validateNoOverlap(timeRanges);
            }
            timeSharingDemandRequests.forEach(
                    timeSharingDemandRequest -> {
                        timeSharingDemandRequest.setProjectId(projectId);
                        this.addDemandItem(timeSharingDemandRequest);
                    });
        }
    }
}
