package com.wifochina.modules.upload.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.wifochina.modules.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 项目文件实体类
 *
 * <AUTHOR>
 * @since 2025-08-19
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("t_project_file")
@ApiModel(value = "ProjectFileEntity", description = "项目文件实体")
public class ProjectFileEntity extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /** 主键ID */
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value = "主键ID")
    private String id;

    /** 项目ID */
    @ApiModelProperty(value = "项目ID")
    private String projectId;

    /** 文件路径（OSS中的完整路径） */
    @ApiModelProperty(value = "文件路径")
    private String filePath;

    /** 原始文件名 */
    @ApiModelProperty(value = "原始文件名")
    private String fileName;

    /** 文件大小（字节） */
    @ApiModelProperty(value = "文件大小")
    private Long fileSize;

    /** 文件类型 */
    @ApiModelProperty(value = "文件类型")
    private String fileType;

    /** 文件扩展名 */
    @ApiModelProperty(value = "文件扩展名")
    private String fileExtension;

    /** 文件内容（base64编码） */
    @ApiModelProperty(value = "文件内容（base64编码）")
    private String fileContent;
}
