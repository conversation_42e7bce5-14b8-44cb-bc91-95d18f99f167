package com.wifochina.modules.diagram.service;

import com.wifochina.modules.diagram.VO.MaxDemandVo;
import com.wifochina.modules.diagram.VO.StringValueVO;
import com.wifochina.modules.diagram.VO.ValueVO;
import com.wifochina.modules.diagram.request.CapacityRequest;
import com.wifochina.modules.diagram.request.RequestWithGroupId;
import com.wifochina.modules.group.entity.GroupEntity;
import org.springframework.data.util.Pair;

import javax.validation.constraints.Max;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2024-02-27 9:39 AM
 */
public interface DemandDiagramService {
    /**
     * @param requestWithGroupId 请求的group id以及日期
     * @return List<ValueVO>
     */
    List<ValueVO> getOriginalDemandByCalculate(RequestWithGroupId requestWithGroupId);

    List<ValueVO> getOriginalDemandByCalculateWithApparent(RequestWithGroupId requestWithGroupId);

    /**
     * @param rateRequest 请求的及日期
     * @return List<ValueVO>
     */
    Map<String, Map<String, List<ValueVO>>> getDemandRate(RequestWithGroupId requestWithGroupId);

    Pair<List<ValueVO>, List<StringValueVO>> getControlPowerRate(RequestWithGroupId rateRequest);

    Map<String, Map<String, List<ValueVO>>> getMaxDemand(RequestWithGroupId rateRequest);

    /**
     * 查询 分时的最大需量
     *
     * @param requestWithGroupId : request
     * @return : Map<Long, List<MaxDemandVo>>
     */
    Map<Long, List<MaxDemandVo>> getTimeSharingMaxDemandNew(RequestWithGroupId requestWithGroupId)
            throws InterruptedException;

    Map<String, List<ValueVO>> getMaxDemandMeterMeasurement(RequestWithGroupId requestWithGroupId);

    Map<String, Map<String, List<ValueVO>>> getMaxDemandRateFromDb(
            RequestWithGroupId rateRequest, boolean isMeasurementSearch);

    List<ValueVO> getCapacity(CapacityRequest capacityRequest);

    Map<Long, List<MaxDemandVo>>   getMaxDemandRateFromDbNew(RequestWithGroupId rateRequest, boolean b);

    Map<Long, List<MaxDemandVo>> getRealTimeMaxDemand(RequestWithGroupId rateRequest, GroupEntity group) throws InterruptedException;
}
