package com.wifochina.modules.event.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.wifochina.common.constants.AlarmConstants;
import com.wifochina.modules.alarm.dtos.AlarmCacheDTO;
import com.wifochina.modules.event.entity.AlarmSwitchEntity;
import com.wifochina.modules.event.entity.EventCodeEntity;
import com.wifochina.modules.event.entity.MeterEventCodeEntity;
import com.wifochina.modules.event.enums.HideCodeLabelEnums;
import com.wifochina.modules.event.mapper.AlarmSwitchMapper;
import com.wifochina.modules.event.request.AlarmSwitchRequest;
import com.wifochina.modules.event.request.HideCodePageRequest;
import com.wifochina.modules.event.service.AlarmSwitchService;
import com.wifochina.modules.event.service.EmsEventCodeService;
import com.wifochina.modules.event.service.MeterEventCodeService;
import com.wifochina.modules.oauth.util.WebUtils;
import com.wifochina.modules.project.entity.ProjectExtEntity;
import com.wifochina.modules.project.service.ProjectExtService;

import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/**
 * 告警开关服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-08
 */
@Service
public class AlarmSwitchServiceImpl extends ServiceImpl<AlarmSwitchMapper, AlarmSwitchEntity>
        implements AlarmSwitchService {

    @Resource private ProjectExtService projectExtService;
    @Resource private EmsEventCodeService eventCodeService;
    @Resource private MeterEventCodeService meterEventCodeService;
    @Resource private StringRedisTemplate stringRedisTemplate;

    @Override
    public IPage<EventCodeEntity> getAlarmSwitchEventCode(
            String projectId, HideCodePageRequest request) {
        ProjectExtEntity projectExtEntity =
                projectExtService
                        .lambdaQuery()
                        .eq(ProjectExtEntity::getId, WebUtils.projectId.get())
                        .one();
        boolean enabledAll = false;
        if (request.getLabel() == HideCodeLabelEnums.EMS_KERNEL.getLabel()) {
            if (projectExtEntity.getAllEmsKernelAlarmEnabled()) {
                enabledAll = true;
            }
        }
        if (request.getLabel() == HideCodeLabelEnums.EMS_SUB_DEVICE.getLabel()) {
            if (projectExtEntity.getAllEmsSubDeviceAlarmEnabled()) {
                enabledAll = true;
            }
        }
        List<AlarmSwitchEntity> alarmSwitchList =
                this.lambdaQuery()
                        .eq(AlarmSwitchEntity::getProjectId, projectId)
                        .eq(AlarmSwitchEntity::getType, request.getLabel())
                        .list();
        // 提取事件代码列表，并做非空判断
        List<String> eventCodes =
                alarmSwitchList.stream()
                        .map(AlarmSwitchEntity::getEventCode)
                        .collect(Collectors.toList());
        if (!enabledAll && eventCodes.isEmpty()) {
            return null;
        }
        // 分页查询事件代码，使用提取的事件代码列表
        return eventCodeService.page(
                Page.of(request.getPageNum(), request.getPageSize()),
                Wrappers.lambdaQuery(EventCodeEntity.class)
                        .eq(request.getLabel() == 1, EventCodeEntity::getDeviceType, "EMS Kernel")
                        .ne(request.getLabel() == 2, EventCodeEntity::getDeviceType, "EMS Kernel")
                        .in(!enabledAll, EventCodeEntity::getEventCode, eventCodes));
    }

    @Override
    public IPage<MeterEventCodeEntity> getMeterAlarmSwitchEventCode(
            String projectId, HideCodePageRequest request) {
        ProjectExtEntity projectExtEntity =
                projectExtService.lambdaQuery().eq(ProjectExtEntity::getId, projectId).one();
        boolean enabledAll = projectExtEntity.getAllMeterAlarmEnabled();
        List<AlarmSwitchEntity> alarmSwitchList =
                this.lambdaQuery()
                        .eq(AlarmSwitchEntity::getProjectId, projectId)
                        .eq(AlarmSwitchEntity::getType, request.getLabel())
                        .list();
        // 提取事件代码列表，并做非空判断
        List<String> eventCodes =
                alarmSwitchList.stream()
                        .map(AlarmSwitchEntity::getEventCode)
                        .collect(Collectors.toList());
        if (!enabledAll && eventCodes.isEmpty()) {
            return null;
        }
        // 分页查询事件代码，使用提取的事件代码列表
        return meterEventCodeService.page(
                Page.of(request.getPageNum(), request.getPageSize()),
                Wrappers.lambdaQuery(MeterEventCodeEntity.class)
                        .in(!enabledAll, MeterEventCodeEntity::getEventCode, eventCodes));
    }

    @Override
    public List<AlarmSwitchEntity> getAlarmSwitchList(String projectId) {
        return baseMapper.getAlarmSwitchList(projectId);
    }

    @Override
    public void updateSwitch(AlarmSwitchRequest alarmSwitchRequest) {
        if (alarmSwitchRequest.getEnableAll()) {
            // 设置全局告警开关
            if (alarmSwitchRequest.getLabel() == 1) {
                projectExtService
                        .lambdaUpdate()
                        .set(
                                ProjectExtEntity::getAllEmsKernelAlarmEnabled,
                                alarmSwitchRequest.getEnableAll())
                        .eq(ProjectExtEntity::getId, WebUtils.projectId.get())
                        .update();
            }
            if (alarmSwitchRequest.getLabel() == 2) {
                projectExtService
                        .lambdaUpdate()
                        .set(
                                ProjectExtEntity::getAllEmsSubDeviceAlarmEnabled,
                                alarmSwitchRequest.getEnableAll())
                        .eq(ProjectExtEntity::getId, WebUtils.projectId.get())
                        .update();
            }
            if (alarmSwitchRequest.getLabel() == 3) {
                projectExtService
                        .lambdaUpdate()
                        .set(
                                ProjectExtEntity::getAllMeterAlarmEnabled,
                                alarmSwitchRequest.getEnableAll())
                        .eq(ProjectExtEntity::getId, WebUtils.projectId.get())
                        .update();
            }
        } else {
            // 设置单个告警开关
            if (alarmSwitchRequest.getLabel() == 1) {
                projectExtService
                        .lambdaUpdate()
                        .set(ProjectExtEntity::getAllEmsKernelAlarmEnabled, false)
                        .eq(ProjectExtEntity::getId, WebUtils.projectId.get())
                        .update();
            }
            if (alarmSwitchRequest.getLabel() == 2) {
                projectExtService
                        .lambdaUpdate()
                        .set(ProjectExtEntity::getAllEmsSubDeviceAlarmEnabled, false)
                        .eq(ProjectExtEntity::getId, WebUtils.projectId.get())
                        .update();
            }
            if (alarmSwitchRequest.getLabel() == 3) {
                projectExtService
                        .lambdaUpdate()
                        .set(ProjectExtEntity::getAllMeterAlarmEnabled, false)
                        .eq(ProjectExtEntity::getId, WebUtils.projectId.get())
                        .update();
            }

            List<AlarmSwitchEntity> list =
                    CollUtil.isEmpty(alarmSwitchRequest.getEventCode())
                            ? new ArrayList<>()
                            : alarmSwitchRequest.getEventCode().stream()
                                    .map(
                                            e ->
                                                    new AlarmSwitchEntity(
                                                            WebUtils.projectId.get(),
                                                            e,
                                                            alarmSwitchRequest
                                                                    .getLabel()
                                                                    .toString()))
                                    .collect(Collectors.toList());

            // 先删除现有配置，再保存新配置
            remove(
                    Wrappers.lambdaQuery(AlarmSwitchEntity.class)
                            .eq(AlarmSwitchEntity::getProjectId, WebUtils.projectId.get())
                            .eq(
                                    AlarmSwitchEntity::getType,
                                    alarmSwitchRequest.getLabel().toString()));
            saveBatch(list);
            // 清除缓存
            clearCache(WebUtils.projectId.get());
        }
    }

    @Override
    public Map<String, Object> listAlarmSwitch(HideCodePageRequest request) {
        IPage<EventCodeEntity> page = getAlarmSwitchEventCode(WebUtils.projectId.get(), request);
        return listAlarmSwitch(page);
    }

    @Override
    public Map<String, Object> listMeterAlarmSwitch(HideCodePageRequest request) {
        IPage<MeterEventCodeEntity> page =
                getMeterAlarmSwitchEventCode(WebUtils.projectId.get(), request);
        return listAlarmSwitch(page);
    }

    private Map<String, Object> listAlarmSwitch(IPage page) {
        ProjectExtEntity projectExtEntity = projectExtService.getById(WebUtils.projectId.get());
        ObjectMapper objectMapper = new ObjectMapper();
        Map<String, Object> result = new HashMap<>();
        try {
            if (page != null) {
                // 将 page 转为 Map
                result = objectMapper.convertValue(page, new TypeReference<>() {});
            }
            // 增加自定义属性
            result.put("allEmsKernelAlarmEnabled", projectExtEntity.getAllEmsKernelAlarmEnabled());
            result.put(
                    "allEmsSubDeviceAlarmEnabled",
                    projectExtEntity.getAllEmsSubDeviceAlarmEnabled());
            result.put("allMeterAlarmEnabled", projectExtEntity.getAllMeterAlarmEnabled());
            // 返回带有额外属性的结果
            return result;
        } catch (Exception e) {
            log.error("转换失败: " + e.getMessage(), e);
        }
        return result;
    }

    @Override
    public AlarmCacheDTO listAlarmSwitch(String projectId) {
        List<AlarmSwitchEntity> alarmSwitchList = getAlarmSwitchList(projectId);
        ProjectExtEntity projectExtEntity = projectExtService.getById(projectId);
        return AlarmCacheDTO.builder()
                .allEmsKernelAlarmEnabled(projectExtEntity.getAllEmsKernelAlarmEnabled())
                .allEmsSubDeviceAlarmEnabled(projectExtEntity.getAllEmsSubDeviceAlarmEnabled())
                .allMeterAlarmEnabled(projectExtEntity.getAllMeterAlarmEnabled())
                .alarmSwitchList(alarmSwitchList)
                .build();
    }

    /** 清除缓存 */
    public void clearCache(String projectId) {
        String redisKey = AlarmConstants.buildAlarmSwitchRedisKey(projectId);
        stringRedisTemplate.delete(redisKey);
    }
}
