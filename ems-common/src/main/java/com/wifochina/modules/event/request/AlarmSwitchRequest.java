package com.wifochina.modules.event.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import lombok.Data;

import java.util.List;

/**
 * 告警开关请求
 *
 * <AUTHOR>
 * @since 2025-07-08
 */
@Data
@ApiModel(value = "告警开关请求")
public class AlarmSwitchRequest {

    @ApiModelProperty(value = "事件编码")
    private List<String> eventCode;

    @ApiModelProperty(value = "分类标签 1 ems-kernel，2 ems-子设备的 3 meter")
    private Integer label;

    @ApiModelProperty(value = "是否开启所有告警")
    private Boolean enableAll;
}
