package com.wifochina.modules.event.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.wifochina.modules.alarm.dtos.AlarmCacheDTO;
import com.wifochina.modules.event.entity.AlarmSwitchEntity;
import com.wifochina.modules.event.entity.EventCodeEntity;
import com.wifochina.modules.event.entity.MeterEventCodeEntity;
import com.wifochina.modules.event.request.AlarmSwitchRequest;
import com.wifochina.modules.event.request.HideCodePageRequest;

import java.util.List;
import java.util.Map;

/**
 * 告警开关服务类
 *
 * <AUTHOR>
 * @since 2025-07-08
 */
public interface AlarmSwitchService extends IService<AlarmSwitchEntity> {

    /**
     * 查询告警开关的事件编码
     *
     * @param projectId 项目ID
     * @param hideCodePageRequest 分页请求
     * @return 分页结果
     */
    IPage<EventCodeEntity> getAlarmSwitchEventCode(
            String projectId, HideCodePageRequest hideCodePageRequest);

    /**
     * 查询电表告警开关的事件编码
     *
     * @param projectId 项目ID
     * @param hideCodePageRequest 分页请求
     * @return 分页结果
     */
    IPage<MeterEventCodeEntity> getMeterAlarmSwitchEventCode(
            String projectId, HideCodePageRequest hideCodePageRequest);

    /**
     * 查询告警开关列表
     *
     * @param projectId 项目ID
     * @return 告警开关列表
     */
    List<AlarmSwitchEntity> getAlarmSwitchList(String projectId);

    /**
     * 更新告警开关
     *
     * @param alarmSwitchRequest 告警开关请求
     */
    void updateSwitch(AlarmSwitchRequest alarmSwitchRequest);

    /**
     * 查询告警开关列表
     *
     * @param alarmSwitchPageRequest 告警开关分页请求
     * @return 分页结果
     */
    Map<String, Object> listAlarmSwitch(HideCodePageRequest alarmSwitchPageRequest);

    /**
     * 查询电表告警开关列表
     *
     * @param alarmSwitchPageRequest 告警开关分页请求
     * @return 分页结果
     */
    Map<String, Object> listMeterAlarmSwitch(HideCodePageRequest alarmSwitchPageRequest);

    /**
     * 查询告警开关列表
     *
     * @param projectId 项目ID
     * @return 告警开关列表
     */
    AlarmCacheDTO listAlarmSwitch(String projectId);
}
