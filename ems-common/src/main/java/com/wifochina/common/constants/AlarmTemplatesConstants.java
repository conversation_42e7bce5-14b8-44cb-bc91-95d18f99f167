package com.wifochina.common.constants;


import com.wifochina.modules.configuration.enums.DataCenterEnum;

/**
 * <AUTHOR>
 * @date 2025/8/8 15:27
 * @version 1.0
 */
public interface AlarmTemplatesConstants {

    static String getTemplateByAlarmContent(AlarmContentEnum alarmContent, String dataCenter) {
        if (AlarmContentEnum.PROJECT_FAULT.equals(alarmContent)) {
            return DataCenterEnum.isCN(dataCenter) ? ALARM_CONTENT_0_CN : ALARM_CONTENT_0_EN;
        } else if (AlarmContentEnum.PROJECT_ALARM.equals(alarmContent)) {
            return DataCenterEnum.isCN(dataCenter) ? ALARM_CONTENT_1_CN : ALARM_CONTENT_1_EN;
        } else if(AlarmContentEnum.PROJECT_EARNING.equals(alarmContent)){
            return DataCenterEnum.isCN(dataCenter) ? ALARM_CONTENT_2_CN : ALARM_CONTENT_2_EN;
        } else if(AlarmContentEnum.DEVICE_STOP.equals(alarmContent)) {
            return DataCenterEnum.isCN(dataCenter) ? ALARM_CONTENT_3_CN : ALARM_CONTENT_3_EN;
        } else if(AlarmContentEnum.DEVICE_OFFLINE.equals(alarmContent)) {
            return DataCenterEnum.isCN(dataCenter) ? ALARM_CONTENT_4_CN : ALARM_CONTENT_4_EN;
        } else if(AlarmContentEnum.SYSTEM_EFFICIENCY.equals(alarmContent)){
            return DataCenterEnum.isCN(dataCenter) ? ALARM_CONTENT_5_CN : ALARM_CONTENT_5_EN;
        }
        return null;
    }
    
    String ALARM_CONTENT_0_CN =
            "**Subject**:[${project_name!\"Unknown Project\"}]-系统警报\n"
                    + "\n"
                    + "尊敬的客户，\n"
                    + "\n"
                    + "请注意，在[${occur_time!\"Unknown time\"}]， [${project_name!\"Unknown Project\"}]发生了故障。 \n"
                    + "\n"
                    + "警报说明：\n"
                    + "\n"
                    + "在盘古OS项目《 [${project_name!\"Unknown Project\"}]》中，设备 ${device_name!\"Unknown Device\"} 发生了故障。\n"
                    + "\n"
                    + "请检查项目状态，以确保正常运行。\n"
                    + "\n"
                    + "如果需要进一步的帮助，请联系项目销售或售后。\n"
                    + "\n"
                    + "顺致敬意";

    String ALARM_CONTENT_0_EN =
            "**Subject**:[${project_name!\"Unknown Project\"}] – System Alarm\n"
                    + "\n"
                    + "Dear Customer,\n"
                    + "\n"
                    + "Please be advised that at [${occur_time!\"Unknown time\"}], a fault occurred in [${project_name!\"Unknown Project\"}].\n"
                    + "\n"
                    + "Alarm Description:\n"
                    + "\n"
                    + "A fault has been detected in device named ${device_name!\"Unknown Device\"} within the Pangu OS project “[${project_name!\"Unknown Project\"}]”\n"
                    + "\n"
                    + "Please check the project status to ensure normal operation.\n"
                    + "\n"
                    + "If you require further assistance, please contact the project sales or after-sales team.\n"
                    + "\n"
                    + "Best regards";

    String ALARM_CONTENT_1_CN =
            "**Subject**:[${project_name!\"Unknown Project\"}]-系统警报\n"
                    + "\n"
                    + "尊敬的客户，\n"
                    + "\n"
                    + "请注意，在[${occur_time!\"Unknown time\"}]，[${project_name!\"Unknown Project\"}]发生了告警。 \n"
                    + "\n"
                    + "警报说明：\n"
                    + "\n"
                    + "在盘古OS项目《[${project_name!\"Unknown Project\"}]》中，设备${device_name!\"Unknown Device\"}发生了告警。\n"
                    + "\n"
                    + "请检查项目状态，以确保正常运行。\n"
                    + "\n"
                    + "如果需要进一步的帮助，请联系项目销售或售后。\n"
                    + "\n"
                    + "顺致敬意";

    String ALARM_CONTENT_1_EN =
            "**Subject**:[${project_name!\"Unknown Project\"}] – System Alert\n"
                    + "\n"
                    + "Dear Customer,\n"
                    + "\n"
                    + "Please be informed that at [${occur_time!\"Unknown time\"}], an alert was triggered in [${project_name!\"Unknown Project\"}].\n"
                    + "\n"
                    + "Alert Description:\n"
                    + "\n"
                    + "An alert occurred on the device named [${device_name!\"Unknown Device\"}] within the Pangu OS project “[${project_name!\"Unknown Project\"}]”\n"
                    + "\n"
                    + "Please check the project status to ensure normal operation.\n"
                    + "\n"
                    + "If you require further assistance, please contact the project sales or after-sales team.\n"
                    + "\n"
                    + "Best regards";

    String ALARM_CONTENT_2_CN =
            "**Subject**:[${project_name!\"Unknown Project\"}]-系统警报\n"
                    + "\n"
                    + "尊敬的客户，\n"
                    + "\n"
                    + "请注意，发现[${occur_time!\"Unknown time\"}] [${project_name!\"Unknown Project\"}]收益异常。 \n"
                    + "\n"
                    + "警报说明：\n"
                    + "\n"
                    + "在盘古OS项目《${project_name!\"Unknown Project\"}》中，[${occur_time!\"Unknown time\"}]收益:[${actual_value!\"Unknown actual_value\"}]，对比上周同日[${last_week_time!\"Unknown time\"}]收益:[${last_week_profit!\"Unknown last_week_profit\"}]，收益偏差率大于[${deviation!\"Unknown deviation\"}]。\n"
                    + "\n"
                    + "请检查项目状态，以确保正常运行。\n"
                    + "\n"
                    + "如果需要进一步的帮助，请联系项目销售或售后。\n"
                    + "\n"
                    + "顺致敬意";

    String ALARM_CONTENT_2_EN =
            "**Subject**:[${project_name!\"Unknown Project\"}] – System Alert\n"
                    + "\n"
                    + "Dear Customer,\n"
                    + "\n"
                    + "Please be informed that on [${occur_time!\"Unknown time\"}], an abnormal revenue was detected in [${project_name!\"Unknown Project\"}].\n"
                    + "\n"
                    + "Alert Description:\n"
                    + "\n"
                    + "In the Pangu OS project “[${project_name!\"Unknown Project\"}]” the revenue on [${occur_time!\"Unknown time\"}] was [${actual_value!\"Unknown actual_value\"}], compared to [${last_week_profit!\"Unknown profit\"}] on the same day last week [${last_week_time!\"Unknown time\"}], showing a revenue deviation greater than [${deviation!\"Unknown deviation\"}].\n"
                    + "\n"
                    + "Please check the project status to ensure normal operation.\n"
                    + "\n"
                    + "If you require further assistance, please contact the project sales or after-sales team.\n"
                    + "\n"
                    + "Best regards";

    String ALARM_CONTENT_3_CN =
            "**Subject**:[${project_name!\"Unknown Project\"}]-系统警报\n"
                    + "\n"
                    + "尊敬的客户，\n"
                    + "\n"
                    + "请注意，在[${occur_time!\"Unknown time\"}]，发现[${project_name!\"Unknown Project\"}]有设备异常停机。 \n"
                    + "\n"
                    + "警报说明：\n"
                    + "\n"
                    + "在盘古OS项目[${project_name!\"Unknown Project\"}]中，有设备异常停机超过[${duration!\"Unknown duration\"}]分钟了。\n"
                    + "\n"
                    + "请检查项目状态，以确保正常运行。\n"
                    + "\n"
                    + "如果需要进一步的帮助，请联系项目销售或售后。\n"
                    + "\n"
                    + "顺致敬意";

    String ALARM_CONTENT_3_EN =
            "**Subject**:[${project_name!\"Unknown Project\"}] – System Alert\n"
                    + "\n"
                    + "Dear Customer,\n"
                    + "\n"
                    + "Please be advised that at [${occur_time!\"Unknown time\"}], abnormal equipment shutdown was detected in [${project_name!\"Unknown Project\"}].\n"
                    + "\n"
                    + "Alert Description:\n"
                    + "\n"
                    + "In the Pangu OS project “[${project_name!\"Unknown Project\"}]” there are devices that have been shut down abnormally for over [${duration!\"Unknown duration\"}] minutes.\n"
                    + "\n"
                    + "Please check the project status to ensure normal operation.\n"
                    + "\n"
                    + "If you require further assistance, please contact the project sales or after-sales team.\n"
                    + "\n"
                    + "Best regards";

    String ALARM_CONTENT_4_CN =
            "**Subject**:[${project_name!\"Unknown Project\"}]-系统警报\n"
                    + "\n"
                    + "尊敬的客户，\n"
                    + "\n"
                    + "请注意，在[${occur_time!\"Unknown time\"}]，发现[${project_name!\"Unknown Project\"}]有设备异常离线。 \n"
                    + "\n"
                    + "警报说明：\n"
                    + "\n"
                    + "在盘古OS项目[${project_name!\"Unknown Project\"}]中，有设备异常离线超过[${duration!\"Unknown duration\"}]分钟了。\n"
                    + "\n"
                    + "请检查项目状态，以确保正常运行。\n"
                    + "\n"
                    + "如果需要进一步的帮助，请联系项目销售或售后。\n"
                    + "\n"
                    + "顺致敬意";

    String ALARM_CONTENT_4_EN =
            "**Subject**:[${project_name!\"Unknown Project\"}] – System Alert\n"
                    + "\n"
                    + "Dear Customer,\n"
                    + "\n"
                    + "Please be advised that at [${occur_time!\"Unknown time\"}], abnormal device offline status was detected in [${project_name!\"Unknown Project\"}].\n"
                    + "\n"
                    + "Alert Description:\n"
                    + "\n"
                    + "In the Pangu OS project “[${project_name!\"Unknown Project\"}]” there are devices that have been offline abnormally for over [${duration!\"Unknown duration\"}] minutes.\n"
                    + "\n"
                    + "Please check the project status to ensure normal operation.\n"
                    + "\n"
                    + "If you require further assistance, please contact the project sales or after-sales team.\n"
                    + "\n"
                    + "Best regards";

    String ALARM_CONTENT_5_CN =
            "**Subject**:[${project_name!\"Unknown Project\"}]-系统警报\n"
                    + "\n"
                    + "尊敬的客户，\n"
                    + "\n"
                    + "请注意，发现 [${occur_time!\"Unknown time\"}] [${project_name!\"Unknown Project\"}]系统效率异常。 \n"
                    + "\n"
                    + "警报说明：\n"
                    + "\n"
                    + "在盘古OS项目[${project_name!\"Unknown Project\"}]中，[${occur_time!\"Unknown time\"}]效率:[${actual_value!\"Unknown actual_value" +
                    "" +
                    "\"}]，低于效率提醒阀值[${deviation!\"Unknown deviation\"}]。\n"
                    + "\n"
                    + "请检查项目状态，以确保正常运行。\n"
                    + "\n"
                    + "如果需要进一步的帮助，请联系项目销售或售后。\n"
                    + "\n"
                    + "顺致敬意";

    String ALARM_CONTENT_5_EN =
            "**Subject**:[${project_name!\"Unknown Project\"}] – System Alert\n"
                    + "\n"
                    + "Dear Customer,\n"
                    + "\n"
                    + "Please be informed that on [${occur_time!\"Unknown time\"}], an abnormal system efficiency was detected in [${project_name!\"Unknown Project\"}]\n"
                    + "\n"
                    + "Alert Description:\n"
                    + "\n"
                    + "In the Pangu OS project “[${project_name!\"Unknown Project\"}]” the efficiency on [${occur_time!\"Unknown time\"}] was [${actual_value!\"Unknown actual_value\"}], which is below the efficiency alert threshold of [${deviation!\"Unknown deviation\"}].\n"
                    + "\n"
                    + "Please check the project status to ensure normal operation.\n"
                    + "\n"
                    + "If you require further assistance, please contact the project sales or after-sales team.\n"
                    + "\n"
                    + "Best regards";
}
