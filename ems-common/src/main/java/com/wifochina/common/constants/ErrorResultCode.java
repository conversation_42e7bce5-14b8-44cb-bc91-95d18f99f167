package com.wifochina.common.constants;

/**
 * @since 2022/3/14 15:59
 * <AUTHOR>
 * @version 1.0
 */
public enum ErrorResultCode {
    CUSTOM_MSG_ERROR("CUSTOM_MSG_ERROR"),

    /** 系统内部错误 */
    INTERNAL_SERVER_ERROR("INTERNAL_SERVER_ERROR"),
    /** ID 不能为空 */
    ILLEGAL_ID_NULL("ILLEGAL_ID_NULL"),
    /** 系统内部错误-分发失败 */
    DISTRIBUTION_FAILED("DISTRIBUTION_FAILED"),
    /** 参数错误 */
    ILLEGAL_ARGUMENT("ILLEGAL_ARGUMENT"),
    /** 业务错误 */
    SERVICE_EXCEPTION("SERVICE_EXCEPTION"),
    /** 时间已重叠 */
    TIME_OVERLAPPED("TIME_OVERLAPPED"),
    /** 日期已重叠 */
    DATE_OVERLAPPED("DATE_OVERLAPPED"),
    /** 时间已重叠 */
    DATETIME_IS_NULL("DATETIME_IS_NULL"),
    /** 业务错误 */
    REPEAT_IMPORT("REPEAT_IMPORT"),
    /** 时间未排满 */
    TIME_IS_NOT_FULL("TIME_IS_NOT_FULL"),
    /** 日期未排满 */
    DATE_IS_NOT_FULL("DATE_IS_NOT_FULL"),
    /** 开始时间大于结束时间 */
    START_GE_END_TIME("START_GE_END_TIME"),
    /** 非法的数据格式，参数没有经过校验 */
    ILLEGAL_DATA("ILLEGAL_DATA"),
    /** 超过项目上传文件大小限制 */
    MULTIPART_TOO_LARGE("MULTIPART_TOO_LARGE"),
    /** 非法状态 */
    ILLEGAL_STATE("ILLEGAL_STATE"),
    /** 缺少参数 */
    MISSING_ARGUMENT("MISSING_ARGUMENT"),
    /** 非法访问 */
    ILLEGAL_ACCESS("ILLEGAL_ACCESS"),
    /** 登录失败 */
    LOGIN_FAIL("LOGIN_FAIL"),
    /** 用户名或者密码错误 */
    USER_PASSWORD_INVALID("USER_PASSWORD_INVALID"),
    /** 两次密码不一致 */
    PASSWORD_INCONSISTENT("PASSWORD_INCONSISTENT"),
    /** AD连接失败 */
    AD_URL_INVALID("AD_URL_INVALID"),
    /** 用户名无效 */
    USER_INVALID("USER_INVALID"),
    /** 角色名重复 */
    ROLE_REPEAT("ROLE_REPEAT"),
    /** 密码无效 */
    PWD_INVALID("PWD_INVALID"),
    /** 权限不足 */
    UNAUTHORIZED("UNAUTHORIZED"),
    /** 没有数据 */
    NO_DATA("NO_DATA"),
    /** 错误的请求 */
    METHOD_NOT_ALLOWED("METHOD_NOT_ALLOWED"),
    /** 参数错误 */
    ILLEGAL_ARGUMENT_TYPE("ILLEGAL_ARGUMENT_TYPE"),
    /** 与协调控制器连接错误 */
    FAIL_CONNECT_GOCONTROL("FAIL_CONNECT_GOCONTROL"),
    /** "not" in string */
    NO_INT_STRING("NO_INT_STRING"),
    /** */
    TO_LIMIT("TO_LIMIT"),
    /** 密码最小长度限制 */
    PASSWORD_MIN_LENGTH_FAIL("PASSWORD_MIN_LENGTH_FAIL"),
    /** 用户名已存在 */
    USER_EXISTS("USER_EXISTS"),

    /** 用户不存在 */
    USER_NOT_EXISTS("USER_NOT_EXISTS"),
    /** 字段超过长度 */
    FILED_MIN_LENGTH_FAIL("%s_MAX_LENGTH_FAIL"),
    /** */
    DATA_REFERENCED("DATA_REFERENCED"),
    /** */
    DATA_REPEAT("DATA_REPEAT"),
    /** 密码最大长度限制 */
    PASSWORD_MAX_LENGTH_FAIL("PASSWORD_MAX_LENGTH_FAIL"),
    /** 密码为空 */
    PASSWORD_IS_EMPTY("PASSWORD_IS_EMPTY"),
    /** 用户密码错误 */
    USER_PASSWORD_MISTYPED("USER_PASSWORD_MISTYPED"),
    /** 密码不一致 */
    PASSWORD_ARE_INCONSISTENT("PASSWORD_ARE_INCONSISTENT"),
    /** 密码格式错误（6-16，英文数字） */
    PASSWORD_FORMAT("PASSWORD_FORMAT"),
    /** 用户名格式错误（5-24，英文数字） */
    USERNAME_FORMAT("USERNAME_FORMAT"),
    /** 连接成功 */
    CONNECT_SUCCESS("CONNECT_SUCCESS"),
    /** SOC 越界 */
    SOC_EXCEED("SOC_EXCEED"),
    /** 功率因数控制值 越界 */
    POWER_FACTOR_CONTROL_VALUE_EXCEED("POWER_FACTOR_CONTROL_VALUE_EXCEED"),
    /** ID 不能为空 */
    IP_ILLEGAL("IP_ILLEGAL"),
    /** 区域已经存在 */
    AREA_EXISTS("AREA_EXISTS"),
    /** 访问过于频繁，请稍候再试 */
    ACCESS_LIMIT_OVER_COUNT("ACCESS_LIMIT_OVER_COUNT"),
    /** 服务器限流异常，请稍候再试 */
    ACCESS_LIMIT_SERVER_ERROR("ACCESS_LIMIT_SERVER_ERROR"),
    /** 手机号未注册 */
    PHONE_IS_NOT_REGISTER("PHONE_IS_NOT_REGISTER"),
    /** 手机号已注册 */
    PHONE_IS_REGISTER("PHONE_IS_REGISTER"),
    /** 手机号不符合格式 */
    PHONE_ILLEGAL("PHONE_ILLEGAL"),
    /** 验证码不正确 */
    CODE_IS_INCORRECT("CODE_IS_INCORRECT"),
    ACCOUNT_IS_LOCK("ACCOUNT_IS_LOCK"),
    /** 验证码已过期 */
    CODE_IS_EXPIRE("CODE_IS_EXPIRE"),
    /** 验证码一分钟只能发一次 */
    SMS_SEND_ONE_MINUTE("SMS_SEND_ONE_MINUTE"),
    /** 连接失败 */
    CONNECT_FAILED("CONNECT_FAILED"),
    ORDER_NUM_REPEAT("ORDER_NUM_REPEAT"),
    /** 配置已存在 */
    PREDICTION_CONFIG_EXISTS("PREDICTION_CONFIG_EXISTS"),
    /** 序号已存在 */
    INDEX_EXIST("INDEX_EXIST"),
    /** ip端口已存在 */
    IP_PORT_EXIST("IP_PORT_EXIST"),
    /** 连接失败 */
    DEVICE_NOT_EXISTS("DEVICE_NOT_EXISTS"),
    /** 能充放电数据不存在 */
    ENERGY_STORAGE_DATA_NOT_EXIST("ENERGY_STORAGE_DATA_NOT_EXIST"),
    /** 需量控制数据不存在 */
    DEMAND_CONTROL_DATA_NOT_EXIST("ENERGY_STORAGE_DATA_NOT_EXIST"),
    WIND_POWER_DATA_NOT_EXIST("WIND_POWER_DATA_NOT_EXIST"),
    PV_POWER_DATA_NOT_EXIST("PV_POWER_DATA_NOT_EXIST"),
    /** 点位 column找不到 */
    POINT_COLUMN_NOT_FIND("POINT_COLUMN_NOT_FIND"),
    EMAIL_IS_NOT_REGISTER("EMAIL_IS_NOT_REGISTER"),
    EMAIL_IS_REGISTER("EMAIL_IS_REGISTER"),
    ILLEGAL_EMAIL("ILLEGAL_EMAIL"),
    FAVOURITE_PROJECT_ADDED("FAVOURITE_PROJECT_ADDED"),
    CLOUD_LARGESCREEN_NOT_OPEN("CLOUD_LARGESCREEN_NOT_OPEN"),
    CNA_NOT_CANCEL_NO_REGISTER("CNA_NOT_CANCEL_NO_REGISTER"),
    LANGUAGE_NEED("LANGUAGE_NEED"),

    PRICE_TEMPLATE_NOT_EXIST("PRICE_TEMPLATE_NOT_EXIST"),

    ELECTRIC_NOT_CONFIG("ELECTRIC_NOT_CONFIG"),
    JUST_ONLY_DYNAMIC_PRICE("JUST_ONLY_DYNAMIC_PRICE"),
    COUNTRY_CHINA_NOT_FIND("COUNTRY_CHINA_NOT_FIND"),

    DYNAMIC_PRICE_NEED_PARAMS("DYNAMIC_PRICE_NEED_PARAMS"),

    SYSTEM_GROUP_NOT_EXIST("SYSTEM_GROUP_NOT_EXIST"),

    ACCOUNT_LIST_PAGE_ERROR("ACCOUNT_LIST_PAGE_ERROR"),
    ROLE_LIST_PAGE_ERROR("ROLE_LIST_PAGE_ERROR"),

    ACCOUNT_SYSTEM_AUTHORIZATION_ROLE_ERROR("ACCOUNT_SYSTEM_AUTHORIZATION_ROLE_ERROR"),
    ACCOUNT_SYSTEM_USER_LIST_ERROR("ACCOUNT_SYSTEM_USER_LIST_ERROR"),
    ACCOUNT_SYSTEM_EMAIL_CHECK_ERROR("ACCOUNT_SYSTEM_EMAIL_CHECK_ERROR"),
    ACCOUNT_SYSTEM_CAPTCHA_CODE_CHECK_ERROR("ACCOUNT_SYSTEM_CAPTCHA_CODE_CHECK_ERROR"),
    ACCOUNT_HAS_ROLE("ACCOUNT_HAS_ROLE"),

    ACCOUNT_SYSTEM_ROLE_CREATE_ERROR("ACCOUNT_SYSTEM_ROLE_CREATE_ERROR"),
    ACCOUNT_SYSTEM_ROLE_DELETE_ERROR("ACCOUNT_SYSTEM_ROLE_DELETE_ERROR"),
    ACCOUNT_SYSTEM_BIND_EMAIL_ERROR("ACCOUNT_SYSTEM_BIND_EMAIL_ERROR"),
    ACCOUNT_SYSTEM_BIND_PHONE_ERROR("ACCOUNT_SYSTEM_BIND_PHONE_ERROR"),

    ACCOUNT_SYSTEM_UPDATE_USER_ERROR("ACCOUNT_SYSTEM_UPDATE_USER_ERROR"),
    ACCOUNT_SYSTEM_CHANGE_PASSWORD_ERROR("ACCOUNT_SYSTEM_CHANGE_PASSWORD_ERROR"),
    ACCOUNT_SYSTEM_FORGET_PASSWORD_ERROR("ACCOUNT_SYSTEM_FORGET_PASSWORD_ERROR"),

    ACCOUNT_SYSTEM_CAPTCHA_CODE_ERROR("ACCOUNT_SYSTEM_CAPTCHA_CODE_ERROR"),
    ACCOUNT_SYSTEM_PHONE_CODE_ERROR("ACCOUNT_SYSTEM_PHONE_CODE_ERROR"),
    ACCOUNT_SYSTEM_EMAIL_CODE_ERROR("ACCOUNT_SYSTEM_EMAIL_CODE_ERROR"),

    CONTROLLABLE_SOCKET_CAN_PARAMS("CONTROLLABLE_SOCKET_CAN_PARAMS"),

    STRATEGY_TEMPLATE_IN_USE("STRATEGY_TEMPLATE_IN_USE"),

    STRATEGY_CONTINUE_CONTROL_NEED_PRE("STRATEGY_CONTINUE_CONTROL_NEED_PRE"),
    STRATEGY_CONTINUE_CONTROL_NEED_OVER_BASE("STRATEGY_CONTINUE_CONTROL_NEED_OVER_BASE"),
    ALARM_NO_MATCH_NOTIFICATION("ALARM_NO_MATCH_NOTIFICATION"),
    ;

    private final String value;

    ErrorResultCode(String value) {
        this.value = value;
    }

    public static ErrorResultCode fromValue(String code) {
        for (ErrorResultCode errorResultCode : ErrorResultCode.values()) {
            if (errorResultCode.value().equals(code)) {
                return errorResultCode;
            }
        }
        return null;
    }

    /** Return the value of this status code. */
    public String value() {
        return this.value;
    }
}
