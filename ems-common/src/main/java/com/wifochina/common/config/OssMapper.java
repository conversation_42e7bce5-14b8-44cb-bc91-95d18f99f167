package com.wifochina.common.config;

import com.aliyun.oss.OSS;
import com.aliyun.oss.model.OSSObject;

import com.wifochina.common.constants.ErrorResultCode;
import com.wifochina.common.exception.ServiceException;
import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Repository;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2025/8/19 14:07
 * @version 1.0
 */
@Slf4j
@Repository
public class OssMapper {

	@Value("${ali.oss.bucket}")
	private String bucket;

	@Resource
	private OSS ossClient;

	public void putObject(String fileName, byte[] bytes) {
		try {
			ossClient.putObject(bucket, fileName, new ByteArrayInputStream(bytes));
		} catch (Exception e) {
			log.warn("oss putObject error: {}", e.getMessage());
            throw new ServiceException(ErrorResultCode.ILLEGAL_DATA.value());
		}
	}

	public void putFile(String bucket, String fileName, byte[] bytes) {
		try {
			ossClient.putObject(bucket, fileName, new ByteArrayInputStream(bytes));
		} catch (Exception e) {
			log.warn("oss putFile error: {}", e.getMessage());
			throw new ServiceException(ErrorResultCode.ILLEGAL_DATA.value());
		}
	}

	/**
	 * 从OSS下载文件
	 *
	 * @param fileName 文件名
	 * @return 文件字节数组
	 */
	public byte[] getObject(String fileName) {
		try {
			OSSObject ossObject = ossClient.getObject(bucket, fileName);
			try (InputStream inputStream = ossObject.getObjectContent();
				 ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {

				byte[] buffer = new byte[1024];
				int length;
				while ((length = inputStream.read(buffer)) != -1) {
					outputStream.write(buffer, 0, length);
				}
				return outputStream.toByteArray();
			}
		} catch (IOException e) {
			log.warn("oss getObject error: {}", e.getMessage());
			throw new ServiceException(ErrorResultCode.ILLEGAL_DATA.value(), "文件下载失败");
		} catch (Exception e) {
			log.warn("oss getObject error: {}", e.getMessage());
			throw new ServiceException(ErrorResultCode.ILLEGAL_DATA.value(), "文件不存在或下载失败");
		}
	}
}
