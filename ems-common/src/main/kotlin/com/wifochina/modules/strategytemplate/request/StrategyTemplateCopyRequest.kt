package com.wifochina.modules.strategytemplate.request

import io.swagger.annotations.ApiModelProperty

/**
 * Created on 2025/4/17 17:16.
 * <AUTHOR>
 */

data class StrategyTemplateCopyRequest(
    var copyId: Long?,
    var fromProjectId: String?,
    val toProjectId: String?,
    var strategyType: String?,
    val templateName: String?,

    //1.4.4 support manager eu au add template
    @ApiModelProperty(value = "模版电价类型") val electricPriceType: String?,
    /** 项目国家  */
    @ApiModelProperty("国家") val country: Int?,

    /** 电价区域字段  */
    @ApiModelProperty("电价区域") val electricPriceArea: String?,
    /** 电价跨度字段  */
    @ApiModelProperty("电价跨度") val electricPriceSpan: String?,
//    val strategyTemplateItemRequests: List<StrategyTemplateItemRequest> = listOf()
)