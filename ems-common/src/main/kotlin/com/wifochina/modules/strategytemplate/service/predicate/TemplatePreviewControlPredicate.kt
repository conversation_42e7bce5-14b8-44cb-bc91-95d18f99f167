package com.wifochina.modules.strategytemplate.service.predicate

import com.baomidou.mybatisplus.extension.kotlin.KtQueryWrapper
import com.wifochina.common.constants.ElectricPriceTypeEnum
import com.wifochina.common.time.MyTimeUtil
import com.wifochina.common.util.EmsConstants
import com.wifochina.modules.electric.vo.ElectricPriceSystemData
import com.wifochina.modules.group.request.go.YearlyStrategy
import com.wifochina.modules.operation.service.ElectricPriceService
import com.wifochina.modules.strategytemplate.common.ElectricPriceDataTransform
import com.wifochina.modules.strategytemplate.common.StrategyCommonUtils
import com.wifochina.modules.strategytemplate.common.StrategyTemplateItemTransform
import com.wifochina.modules.strategytemplate.common.TransformYearlyStrategy
import com.wifochina.modules.strategytemplate.converter.StrategyControlConverter
import com.wifochina.modules.strategytemplate.converter.StrategyConverter
import com.wifochina.modules.strategytemplate.entity.StrategyTemplateItemEntity
import com.wifochina.modules.strategytemplate.entity.transformTimeUnix
import com.wifochina.modules.strategytemplate.enums.ControlPatternLabelEnums
import com.wifochina.modules.strategytemplate.enums.ModelStrategyEnums
import com.wifochina.modules.strategytemplate.enums.StrategyChargeDischargeTypeEnums
import com.wifochina.modules.strategytemplate.service.PredicateContext
import com.wifochina.modules.strategytemplate.service.PredicateModel
import com.wifochina.modules.strategytemplate.service.StrategyTemplateItemControlService
import com.wifochina.modules.strategytemplate.service.StrategyTemplateItemService
import com.wifochina.modules.strategytemplate.service.dependencypredicate.ControlDependencyPatternPredicate
import io.github.oshai.kotlinlogging.KotlinLogging
import org.springframework.stereotype.Component
import java.time.Instant
import java.time.LocalTime
import java.time.ZoneId


private val log = KotlinLogging.logger { }


/**
 * Created on 2025/4/29 14:34.
 * <AUTHOR>
 */
@Component
class TemplatePreviewControlPredicate(
    val electricPriceService: ElectricPriceService,
    val strategyTemplateItemService: StrategyTemplateItemService,
    val strategyTemplateItemControlService: StrategyTemplateItemControlService,
    val controlPatternPredicates: MutableList<out ControlPatternPredicate>,
    val controlDependencyPatternPredicates: MutableList<out ControlDependencyPatternPredicate>,
    val electricPriceDataTransform: ElectricPriceDataTransform,
    val strategyTemplateItemTransform: StrategyTemplateItemTransform,
) : PredicateModel {
    override fun model(): ModelStrategyEnums {
        return ModelStrategyEnums.TEMPLATE_PREVIEW_CONTROL
    }

    override fun predicate(context: PredicateContext): Map<String, List<YearlyStrategy>> {
        var directTransformItem = true
        if (context.project.electricPriceType.equals(ElectricPriceTypeEnum.DYNAMIC_PRICE_PERIOD.name)) {
            directTransformItem = false
        }
        val templateId = context.templatePreviewContext!!.templateId
        // control 模式
        val project = context.project
        val date = context.date ?: MyTimeUtil.getTodayZeroTime(project.timezone)
        val localDate = Instant.ofEpochSecond(date).atZone(ZoneId.of(project.timezone)).toLocalDate()
        var allDayElectricPrice: List<ElectricPriceSystemData.Data> = listOf()
        if (!directTransformItem) {
            allDayElectricPrice = electricPriceService.getElectricPriceSystemDataUsePriceProxy(
                date, project
            )
            if (allDayElectricPrice.isEmpty()) {
                return mapOf()
            }
        }
        val strategyDateStr = StrategyCommonUtils.getStrategyDateStr(project, date)
        //只查询 今天的
        // maybe more but just only choose  today
        val list = strategyTemplateItemService.list(
            KtQueryWrapper(StrategyTemplateItemEntity::class.java).eq(
                project.id != null,
                StrategyTemplateItemEntity::projectId,
                project.id
            )
                .eq(StrategyTemplateItemEntity::templateId, templateId)
        )

        val selfProductConsumerStrategyList = list.filter { it.type!! == 2 }
        //充放电的 需要找到对应的control
        val chargeDischargeStrategyList = list.filter { it.type!! != 2 }
        // 得到 item control的 map
        val strategyControlMap =
            strategyTemplateItemControlService.selectControlByIds(chargeDischargeStrategyList.filter { it.itemControlId != null }
                                                                      .map { it.itemControlId!! })
        val chargeStrategyControlResults = mutableListOf<YearlyStrategy>()
        val dischargeStrategyControlResults = mutableListOf<YearlyStrategy>()

        //自发自用  应该是和simple模式一样的处理方式
        val selfProductConsumerResults = mutableListOf<YearlyStrategy>()
        selfProductConsumerStrategyList.forEach { strategyItem ->
            if (directTransformItem) {
                //国内的直接转换
                selfProductConsumerResults.addAll(strategyTemplateItemTransform.transform(listOf(strategyItem)) {
                    this.project = project
                    this.strategy = StrategyConverter.INSTANCE.strategyTemplateItem2Entity(strategyItem)
                    this.strategyDateStr = strategyDateStr
                })
            } else {
                var endZeroFlag = false
                if (strategyItem.endTime == LocalTime.MIDNIGHT) {
                    endZeroFlag = true
                }
                val strategyContext = StrategyPredicateContext(
                    context, StrategyConverter.INSTANCE.strategyTemplateItem2Entity(strategyItem), allDayElectricPrice
                ).calStrategyPeriodElectricPrices(
                    strategyItem.startTime!!.transformTimeUnix(localDate, ZoneId.of(project.timezone)),
                    if (!endZeroFlag) strategyItem.endTime!!.transformTimeUnix(
                        localDate, ZoneId.of(project.timezone)
                    ) else strategyItem.endTime!!.transformTimeUnix(
                        localDate, ZoneId.of(project.timezone)
                    ) + EmsConstants.ONE_DAY_SECOND
                )
                selfProductConsumerResults.addAll(
                    electricPriceDataTransform.transform(
                        strategyContext.strategyPeriodElectricPrices
                    ) {
                        this.project = project
                        this.strategy = StrategyConverter.INSTANCE.strategyTemplateItem2Entity(strategyItem)
                        this.strategyDateStr = strategyDateStr
                    })
            }
        }
        try {

            chargeDischargeStrategyList.forEach strategy@{ strategyItem ->
                if (directTransformItem) {
                    //国内的直接转换
                    // 不知道这里有没有 如果可以不设置条件 那么这里应该要有
                    val yearlyStrategies = strategyTemplateItemTransform.transform(
                        listOf(strategyItem)
                    ) {
                        this.project = project
                        this.strategy = StrategyConverter.INSTANCE.strategyTemplateItem2Entity(strategyItem)
                        this.strategyDateStr = strategyDateStr
                    }
                    //带冲放的策略 需要最后控制一下 交集问题 ->转成不冲不放
                    when (StrategyChargeDischargeTypeEnums.valueOfByType(strategyItem.type!!)) {
                        StrategyChargeDischargeTypeEnums.CHARGE -> chargeStrategyControlResults.addAll(
                            yearlyStrategies
                        )

                        StrategyChargeDischargeTypeEnums.DISCHARGE -> dischargeStrategyControlResults.addAll(
                            yearlyStrategies
                        )
                    }
                } else {
                    var endZeroFlag = false
                    if (strategyItem.endTime == LocalTime.MIDNIGHT) {
                        endZeroFlag = true
                    }
                    val strategyContext = StrategyPredicateContext(
                        context,
                        StrategyConverter.INSTANCE.strategyTemplateItem2Entity(strategyItem),
                        allDayElectricPrice
                    ).calStrategyPeriodElectricPrices(
                        strategyItem.startTime!!.transformTimeUnix(localDate, ZoneId.of(project.timezone)),
                        if (!endZeroFlag) strategyItem.endTime!!.transformTimeUnix(
                            localDate, ZoneId.of(project.timezone)
                        ) else strategyItem.endTime!!.transformTimeUnix(
                            localDate, ZoneId.of(project.timezone)
                        ) + EmsConstants.ONE_DAY_SECOND
                    )
                    if (strategyContext.strategyPeriodElectricPrices.isEmpty()) {
                        return@strategy
                    }
                    val controlResultMap = mutableMapOf<String, List<ElectricPriceSystemData.Data>>()
                    val transformContext: TransformYearlyStrategy.TransformContext.() -> Unit = {
                        this.project = project
                        this.strategy = StrategyConverter.INSTANCE.strategyTemplateItem2Entity(strategyItem)
                        this.strategyDateStr = strategyDateStr
                    }
                    strategyControlMap[strategyItem.itemControlId]?.let { control ->
                        if (control.priceBaseValueController == false && control.priceBenchmarkController == false && control.priceDifferenceController == false) {
                            when (StrategyChargeDischargeTypeEnums.valueOfByType(strategyItem.type!!)) {
                                StrategyChargeDischargeTypeEnums.CHARGE -> {
                                    chargeStrategyControlResults.add(
                                        strategyTemplateItemTransform.transform(
                                            listOf(strategyItem)
                                        ) {
                                            this.project = project
                                        }[0]
                                    )
                                }

                                StrategyChargeDischargeTypeEnums.DISCHARGE -> {
                                    dischargeStrategyControlResults.add(
                                        strategyTemplateItemTransform.transform(
                                            listOf(strategyItem)
                                        ) {
                                            this.project = project
                                        }[0]
                                    )
                                }
                            }
                            return@strategy
                        }
                        val controlPatternPredicateContext = ControlPatternPredicate.ControlPatternPredicateContext(
                            strategyContext, StrategyControlConverter.INSTANCE.itemControlToStrategyControl(control)
                        )
                        controlPatternPredicates.forEach { predicate ->
                            val predicateResult = predicate.patternPredicate(
                                controlPatternPredicateContext
                            )
                            if (predicateResult.skip) {
                                //直接跳过当前的策略 到下一个策略
                                return@strategy
                            }
                            //结果
                            when (StrategyChargeDischargeTypeEnums.valueOfByType(strategyItem.type!!)) {
                                StrategyChargeDischargeTypeEnums.CHARGE -> controlResultMap[predicate.patternLabel().name] =
                                    predicateResult.matchResult

                                StrategyChargeDischargeTypeEnums.DISCHARGE -> controlResultMap[predicate.patternLabel().name] =
                                    predicateResult.matchResult
                            }
                        }
                        //3个predicate 结束后 要进行continue的predicate执行..
                        val controlResultToIntersect = listOfNotNull(
                            if (control.priceBaseValueController!!) controlResultMap[ControlPatternLabelEnums.BASE_VALUE.name] else null,
                            if (control.priceDifferenceController!!) controlResultMap[ControlPatternLabelEnums.DIFFERENCE_VALUE.name] else null,
                            if (control.priceBenchmarkController!!) controlResultMap[ControlPatternLabelEnums.BENCHMARK_VALUE.name] else null,
                        )
                        if (controlResultToIntersect.isEmpty()) {
                            return@strategy
                        }
                        var intersect =
                            controlResultToIntersect.reduce { acc, list -> acc.intersect(list.toSet()).toList() }
                        controlDependencyPatternPredicates.forEach { dependencyPredicate ->
                            //for extend in future ? ... do you understand
                            intersect = dependencyPredicate.dependencyPatternPredicate(
                                ControlDependencyPatternPredicate.DependencyPredicateContext(
                                    controlPatternPredicateContext, intersect
                                )
                            ).matchResult

                        }
                        val yearlyStrategies = electricPriceDataTransform.transform(intersect, transformContext)
                        when (StrategyChargeDischargeTypeEnums.valueOfByType(strategyItem.type!!)) {
                            StrategyChargeDischargeTypeEnums.CHARGE -> chargeStrategyControlResults.addAll(
                                yearlyStrategies
                            )

                            StrategyChargeDischargeTypeEnums.DISCHARGE -> dischargeStrategyControlResults.addAll(
                                yearlyStrategies
                            )
                        }
                    } ?: run {
                        //无控制..
                        // 不知道这里有没有 如果可以不设置条件 那么这里应该要有
                        val yearlyStrategies = electricPriceDataTransform.transform(
                            strategyContext.strategyPeriodElectricPrices, transformContext
                        )
                        //带冲放的策略 需要最后控制一下 交集问题 ->转成不冲不放
                        when (StrategyChargeDischargeTypeEnums.valueOfByType(strategyItem.type!!)) {
                            StrategyChargeDischargeTypeEnums.CHARGE -> chargeStrategyControlResults.addAll(
                                yearlyStrategies
                            )

                            StrategyChargeDischargeTypeEnums.DISCHARGE -> dischargeStrategyControlResults.addAll(
                                yearlyStrategies
                            )
                        }
                    }

                }
            }
        } catch (e: Exception) {
            log.error { "chargeDischargeStrategyList forEach error : $e" }
        }
        //所有策略结束后 要把冲放2个结果 进行交集 交集要踢出了 表示补充不放
        val result = mutableListOf<List<YearlyStrategy>>()
        val newChargeList = mutableListOf<YearlyStrategy>()
        val newDischargeList = mutableListOf<YearlyStrategy>()

        if (chargeStrategyControlResults.isEmpty() && dischargeStrategyControlResults.isNotEmpty()) {
            newDischargeList.addAll(dischargeStrategyControlResults)
        } else if (dischargeStrategyControlResults.isEmpty() && chargeStrategyControlResults.isNotEmpty()) {
            newChargeList.addAll(chargeStrategyControlResults)
        } else {
            log.info { "chargeStrategyControlResults: $chargeStrategyControlResults" }
            for (charge in chargeStrategyControlResults) {
                val overlappingDischarges = dischargeStrategyControlResults.filter {
                    it.end_minute > charge.start_minute && it.start_minute < charge.end_minute
                }

                val cutCharges = subtractOverlapsFromStrategy(charge, overlappingDischarges)
                newChargeList.addAll(cutCharges)
            }
            log.info { "dischargeStrategyControlResults: $dischargeStrategyControlResults" }
            for (discharge in dischargeStrategyControlResults) {
                val overlappingCharges = chargeStrategyControlResults.filter {
                    it.end_minute > discharge.start_minute && it.start_minute < discharge.end_minute
                }

                val cutDischarges = subtractOverlapsFromStrategy(discharge, overlappingCharges)
                newDischargeList.addAll(cutDischarges)
            }
        }
        log.info { "charge over laps result: $newChargeList" }
        log.info { "discharge over laps result: $newDischargeList" }
        result.add(newChargeList)
        result.add(newDischargeList)
        log.info { "selfProductConsumerResults result:$selfProductConsumerResults" }
        result.add(selfProductConsumerResults)
        val flatten = result.flatten()
        return mapOf(
            strategyDateStr to flatten
        )
    }

    fun subtractOverlapsFromStrategy(
        base: YearlyStrategy, overlaps: List<YearlyStrategy>
    ): List<YearlyStrategy> {
        var currentStart = base.start_minute
        val result = mutableListOf<YearlyStrategy>()

        val sortedOverlaps = overlaps.sortedBy { it.start_minute }

        for (overlap in sortedOverlaps) {
            if (overlap.end_minute <= currentStart) continue
            if (overlap.start_minute >= base.end_minute) break

            if (overlap.start_minute > currentStart) {
                val part = base.clone()
                part.start_minute = currentStart
                part.end_minute = minOf(overlap.start_minute, base.end_minute)
                result.add(part)
            }
            currentStart = maxOf(currentStart, overlap.end_minute)
        }

        if (currentStart < base.end_minute) {
            val part = base.clone()
            part.start_minute = currentStart
            part.end_minute = base.end_minute
            result.add(part)
        }
        return result
    }


}