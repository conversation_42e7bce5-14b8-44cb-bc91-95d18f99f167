package com.wifochina.modules.strategytemplate.service.impl

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper
import com.baomidou.mybatisplus.extension.kotlin.KtQueryWrapper
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl
import com.wifochina.common.constants.ElectricPriceTypeEnum
import com.wifochina.common.constants.ErrorResultCode
import com.wifochina.common.exception.ServiceException
import com.wifochina.common.mdc.MdcKotlinContext
import com.wifochina.common.time.MyTimeUtil.checkTime
import com.wifochina.modules.project.entity.ProjectEntity
import com.wifochina.modules.project.service.ProjectService
import com.wifochina.modules.strategy.entity.StrategyEntity
import com.wifochina.modules.strategytemplate.converter.StrategyTemplateItemControlConverter
import com.wifochina.modules.strategytemplate.converter.StrategyTemplateItemConverter
import com.wifochina.modules.strategytemplate.entity.StrategyControlEntity
import com.wifochina.modules.strategytemplate.entity.StrategyDayTemplateBindEntity
import com.wifochina.modules.strategytemplate.entity.StrategyTemplateItemControlEntity
import com.wifochina.modules.strategytemplate.entity.StrategyTemplateItemEntity
import com.wifochina.modules.strategytemplate.mapper.StrategyDayTemplateBindMapper
import com.wifochina.modules.strategytemplate.mapper.StrategyTemplateItemMapper
import com.wifochina.modules.strategytemplate.mapper.StrategyTemplateMapper
import com.wifochina.modules.strategytemplate.request.StrategyTemplateItemRequest
import com.wifochina.modules.strategytemplate.service.StrategyServiceKt
import com.wifochina.modules.strategytemplate.service.StrategyTemplateItemControlService
import com.wifochina.modules.strategytemplate.service.StrategyTemplateItemService
//import com.wifochina.realtimemodel.entity.QProjectEntity.projectEntity
import kotlinx.coroutines.asCoroutineDispatcher
import kotlinx.coroutines.launch
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.withContext
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional

/**
 * Created on 2025/4/18 15:11.
 * <AUTHOR>
 */
@Service
@Transactional(rollbackFor = [Exception::class])
class StrategyTemplateItemServiceImpl(
    val projectService: ProjectService,
    val strategyServiceKt: StrategyServiceKt,
    val strategyTemplateMapper: StrategyTemplateMapper,
    val strategyDayTemplateBindMapper: StrategyDayTemplateBindMapper,
    val strategyTemplateItemControlService: StrategyTemplateItemControlService,
    val threadPoolTaskExecutor: ThreadPoolTaskExecutor,
) : ServiceImpl<StrategyTemplateItemMapper, StrategyTemplateItemEntity>(), StrategyTemplateItemService {

    /**
     * // ⚠️ 按照分组 groupByStrategyDayTemplateBinds （groupId）进行处理是必须的，避免不同分组间的数据互相干扰。
     * // 假设多个分组在不同月份绑定了同一个模板，如果不按分组区分处理，可能会误删数据。
     *
     * // ✅ 示例说明：
     * // 1. 系统分组（groupId: 系统）
     * //    - 7 月份绑定了模板【测试模版1】
     * //    - 2 月份绑定了模板【测试模版2】
     * //
     * // 2. 容量分组（groupId: 容量）
     * //    - 7 月份绑定了模板【测试模版2】
     * //
     * // ❌ 如果不按分组处理，会出现以下问题：
     * // - 查询 `dayBindInfos` 时会拿到系统分组的 7 月和 2 月绑定信息
     * // - 因此 `strategyDayStrList` 中包含了 "07" 和 "02" 两个月的 整个list  如 02-01...02-28   07-02...07-31
     * // - 当修改了【测试模版2】对应的数据，调用 `strategyServiceKt.remove(...)` 时， 原本是 in 查询 分组 会导致把 系统分组的 7月份的 的也删了 但是这个是 测试模版1的
     *                      strategyServiceKt.remove(
     *                         LambdaQueryWrapper<StrategyEntity>().`in`(StrategyEntity::getStrategyDate, strategyDayStrList)
     *                             .eq(StrategyEntity::getProjectId, request.projectId)
     *                             .in(StrategyEntity::getGroupId, groupIds)
     *                     )
     * //   就会误删系统分组中 **7 月份的模板1 数据**
     * //
     * // ✅ 正确逻辑应当是：
     * // - 只删除系统分组中 **2 月份绑定的测试模版2 的数据**
     * // - 不能影响其他月份，其他分组的数据
     *
     * 刷新 day template的 已经绑定的, 重新生成
     */
    private fun refreshDayTemplateUsers(request: StrategyTemplateItemRequest) {
        // 2025-04-28 10:54:48 add 把已经使用了这个模版的更新一下
        strategyDayTemplateBindMapper.selectList(
            KtQueryWrapper(StrategyDayTemplateBindEntity::class.java).eq(
                StrategyTemplateItemEntity::projectId, request.projectId
            ).eq(StrategyTemplateItemEntity::templateId, request.templateId)
        )?.takeIf { it.isNotEmpty() }?.let { dayBindInfos ->
            val groupByStrategyDayTemplateBinds = dayBindInfos.groupBy { it.groupId!! }
            val groupIds = groupByStrategyDayTemplateBinds.keys
            if (groupIds.isNotEmpty()) {
                val template = strategyTemplateMapper.selectById(request.templateId)
                val templateItemsMap = this.selectItemByTemplateIds(
                    request.projectId!!, listOf(request.templateId)
                )
                runBlocking {
                    withContext(threadPoolTaskExecutor.asCoroutineDispatcher() + MdcKotlinContext()) {
                        groupByStrategyDayTemplateBinds.forEach { groupId, groupDayBindInfos ->
                            launch {
                                val rebuildStrategyEntities = mutableListOf<StrategyEntity>()
                                //这个打开就优化了k
                                val strategyDayStrList =
                                    groupDayBindInfos.map { dayBindInfo -> dayBindInfo.getStrategyDateStr() }
                                //删除 strategy control 这一天的
                                runBlocking {
                                    withContext(threadPoolTaskExecutor.asCoroutineDispatcher() + MdcKotlinContext()) {
                                        groupDayBindInfos.forEach { dayBindInfo ->
                                            launch {
                                                //重新生成
                                                templateItemsMap[request.templateId]?.let {
                                                    val dayStrategyEntities =
                                                        strategyServiceKt.rebuildDayStrategyWithItem(
                                                            dayBindInfo, it, template!!
                                                        )
                                                    //处理 real StrategyEntity
                                                    rebuildStrategyEntities.addAll(dayStrategyEntities)
                                                }
                                            }
                                        }
                                    }
                                }
                                //删除 这里是一次性删除多个
                                strategyServiceKt.remove(
                                    LambdaQueryWrapper<StrategyEntity>().`in`(
                                        StrategyEntity::getStrategyDate, strategyDayStrList
                                    ).eq(StrategyEntity::getProjectId, request.projectId)
                                        .eq(StrategyEntity::getGroupId, groupId)
                                )
                                //重新生成
                                strategyServiceKt.saveBatch(rebuildStrategyEntities)
                            }
                        }
                    }
                }
            }
        }
    }

    /**
     * addItem 新增 item的
     */
    override fun addItem(templateId: Long, request: StrategyTemplateItemRequest) {
        checkTime(templateId, request)
        //2.处理 control
        val itemControlEntity = request.itemControl?.let {
            val controlEntity = StrategyTemplateItemControlConverter.INSTANCE.request2Entity(it)
            if (controlEntity.priceBaseValueController == false && controlEntity.priceBenchmarkController == false && controlEntity.priceDifferenceController == false) {
                if (controlEntity.priceContinueDurationController == true) {
                    throw ServiceException(ErrorResultCode.STRATEGY_CONTINUE_CONTROL_NEED_PRE.value())
                }
            }
            if (controlEntity.priceBaseValueController == true && controlEntity.priceContinueDurationController == true) {
                // 1.4.4 added 连续时长段数 需要大于 电价最值段数 3、如果同时启用 “电价最值”和“连续时段”策略，新增限制，连续时段数要求大于等于电价最值时段数，不然无法添加策略
                controlEntity.priceContinueDurationSegment?.let { segment ->
                    controlEntity.priceBaseValueSectionCount?.let { baseCount ->
                        if (segment < baseCount) {
                            throw ServiceException(ErrorResultCode.STRATEGY_CONTINUE_CONTROL_NEED_OVER_BASE.value())
                        }
                    }
                }
            }
            strategyTemplateItemControlService.save(controlEntity)
            controlEntity
        }
        val strategyTemplateItemEntity = StrategyTemplateItemConverter.INSTANCE.request2Entity(request)
        strategyTemplateItemEntity.templateId = templateId
        strategyTemplateItemEntity.itemControlId = itemControlEntity?.id
        //1.保存strategy item entity
        this.save(strategyTemplateItemEntity)

        // 2025-04-28 10:54:48 add 把已经使用了这个模版的更新一下
//        threadPoolTaskExecutor.submit {
        if (request.projectId != null) {
            refreshDayTemplateUsers(request)
        }
//        }
    }

    fun checkTime(templateId: Long, request: StrategyTemplateItemRequest) {
        //国内和海外的固定 冲冲 放放 冲放  自发自用 都不可交叉
        //其他的海外的 冲冲 放放要校验 冲放不校验
        val electricPriceType: String? = request.projectId?.let {
            val projectEntity: ProjectEntity = projectService.getById(request.projectId) ?: return
            projectEntity.electricPriceType
        } ?: run {
            val strategyTemplateEntity = strategyTemplateMapper.selectById(templateId)
            strategyTemplateEntity.electricPriceType
        }
        if (electricPriceType == null) {
            throw ServiceException(ErrorResultCode.ILLEGAL_ARGUMENT.value())
        }
        var list = listOf<StrategyTemplateItemEntity>()
        when (electricPriceType) {
            ElectricPriceTypeEnum.PEAK_VALLEYS_PRICE_PERIOD.name, ElectricPriceTypeEnum.FIXED_PRICE_PERIOD.name -> {
                list = this.list(
                    KtQueryWrapper(StrategyTemplateItemEntity::class.java).eq(
                        StrategyTemplateItemEntity::templateId, request.templateId
                    )
                        .ne(request.id != null, StrategyTemplateItemEntity::id, request.id)
                        .orderByAsc(StrategyTemplateItemEntity::startTime)
                )
            }

            ElectricPriceTypeEnum.DYNAMIC_PRICE_PERIOD.name -> {
                if (request.type == 2) {
                    list = this.list(
                        KtQueryWrapper(StrategyTemplateItemEntity::class.java).eq(
                            StrategyTemplateItemEntity::templateId, request.templateId
                        )
                            .ne(request.id != null, StrategyTemplateItemEntity::id, request.id)
                            .orderByAsc(StrategyTemplateItemEntity::startTime)
                    )
                } else {
                    list = this.list(
                        KtQueryWrapper(StrategyTemplateItemEntity::class.java).eq(
                            StrategyTemplateItemEntity::templateId, request.templateId
                        )
                            .eq(StrategyTemplateItemEntity::type, request.type)
                            .ne(request.id != null, StrategyTemplateItemEntity::id, request.id)
                            .orderByAsc(StrategyTemplateItemEntity::startTime)
                    )
                }
            }
        }
        if (request.endTime.toSecondOfDay() != 0 && !request.endTime.isAfter(request.startTime)) {
            throw ServiceException(ErrorResultCode.START_GE_END_TIME.value())
        }
        if (list.isEmpty()) return
        // 如果 request 的 endTime 是 00:00:00
        if (request.endTime.toSecondOfDay() == 0) {
            val last = list.last()
            if (last.endTime!!.toSecondOfDay() == 0) {
                throw ServiceException(ErrorResultCode.TIME_OVERLAPPED.value())
            }
            if (request.startTime.isBefore(last.endTime)) {
                throw ServiceException(ErrorResultCode.TIME_OVERLAPPED.value())
            }
        }

        for (item in list) {
            // 如果是自己，跳过（用于更新场景）
            if (request.id == item.id) continue

            if (!request.endTime.isAfter(item.startTime)) {
                break
            }

            if (request.startTime.isBefore(item.endTime) || item.endTime!!.toSecondOfDay() == 0) {
                throw ServiceException(ErrorResultCode.TIME_OVERLAPPED.value())
            }
        }
    }


    override fun updateItem(request: StrategyTemplateItemRequest) {
        this.getById(request.id)?.let { itemEntity ->
            checkTime(request.templateId, request)
            this.updateById(
                StrategyTemplateItemConverter.INSTANCE.request2Entity(
                    request
                ).apply {
                    id = itemEntity.id
                })
            //处理 control
            request.itemControl?.id?.let { itemControlId ->
                strategyTemplateItemControlService.getById(itemControlId)?.let { itemControl ->
                    if (request.itemControl.priceBaseValueController == false && request.itemControl.priceBenchmarkController == false && request.itemControl.priceDifferenceController == false) {
                        if (request.itemControl.priceContinueDurationController == true) {
                            throw ServiceException(ErrorResultCode.STRATEGY_CONTINUE_CONTROL_NEED_PRE.value())
                        }
                    }
                    strategyTemplateItemControlService.updateById(
                        StrategyTemplateItemControlConverter.INSTANCE.request2Entity(
                            request.itemControl
                        ).apply {
                            id = itemControlId
                        })
                }
            }
//            threadPoolTaskExecutor.submit {
            // 2025-04-28 10:54:48 add 把已经使用了这个模版的更新一下
            if (request.projectId != null) {
                refreshDayTemplateUsers(request)
            }
//            }
        }
    }

    override fun deleteItem(request: StrategyTemplateItemRequest) {
        this.getById(request.id)?.let {
            this.removeById(
                request.id,
            )
            it.itemControlId?.let { controlId ->
                //处理control
                strategyTemplateItemControlService.remove(
                    KtQueryWrapper(StrategyTemplateItemControlEntity::class.java).eq(
                        StrategyTemplateItemControlEntity::id, controlId
                    )
                )
            }
//            threadPoolTaskExecutor.submit {
            // 2025-04-28 10:54:48 add 把已经使用了这个模版的更新一下
            if (request.projectId != null) {
                refreshDayTemplateUsers(request)
            }
//            }
        }
    }

    override fun selectItemByTemplateIds(
        projectId: String, templateIds: List<Long>
    ): Map<Long, List<StrategyTemplateItemEntity>> {
        return takeIf { templateIds.isNotEmpty() }?.let {
            this.list(
                KtQueryWrapper(StrategyTemplateItemEntity::class.java).`in`(
                    StrategyTemplateItemEntity::templateId, templateIds
                ).eq(StrategyTemplateItemEntity::projectId, projectId)
            ).groupBy { it.templateId!! }
        } ?: run {
            mapOf()
        }
    }

    override fun selectItemByTemplateIdsAll(
        projectId: String?,
        templateIds: List<Long>
    ): Map<Long, List<StrategyTemplateItemEntity>> {
        return takeIf { templateIds.isNotEmpty() }?.let {
            this.list(
                KtQueryWrapper(StrategyTemplateItemEntity::class.java).`in`(
                    StrategyTemplateItemEntity::templateId, templateIds
                ).eq(projectId != null, StrategyTemplateItemEntity::projectId, projectId)
                    .isNull(projectId == null, StrategyTemplateItemEntity::projectId)
            ).groupBy { it.templateId!! }
        } ?: run {
            mapOf()
        }
    }

}