package com.wifochina.modules.manage.price.controller;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.wifochina.common.constants.ElectricPriceTypeEnum;
import com.wifochina.common.constants.ErrorResultCode;
import com.wifochina.common.exception.ServiceException;
import com.wifochina.common.log.Log;
import com.wifochina.common.page.Result;
import com.wifochina.modules.area.entity.PriceAreaEntity;
import com.wifochina.modules.area.request.PriceAreaElectricVos;
import com.wifochina.modules.area.service.PriceAreaService;
import com.wifochina.modules.electric.vo.ElectricPriceSystemData;
import com.wifochina.modules.income.cache.memorycache.IIncomeMemoryCacheService;
import com.wifochina.modules.log.OperationType;
import com.wifochina.modules.manage.price.service.ElectricPriceCloudService;
import com.wifochina.modules.operation.entity.ElectricPriceEntity;
import com.wifochina.modules.operation.request.RealTimePriceRequest;
import com.wifochina.modules.operation.service.ElectricPriceService;
import com.wifochina.modules.project.entity.ProjectEntity;
import com.wifochina.modules.project.entity.ProjectExtEntity;
import com.wifochina.modules.project.service.ProjectExtService;
import com.wifochina.modules.project.service.ProjectService;

import com.wifochina.modules.strategytemplate.entity.StrategyTemplateEntity;
import com.wifochina.modules.strategytemplate.service.StrategyTemplateService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;

import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/**
 * 前端控制器
 *
 * <AUTHOR>
 * @since 2022-09-05
 */
@RequestMapping("/manage/price")
@RestController
@Api(tags = "manage-02-项目托管价格")
public class ElectricPriceCloudController {

    @Resource private ElectricPriceCloudService electricPriceCloudService;

    @Resource private ProjectService projectService;

    @Resource private ProjectExtService projectExtService;

    @Resource private PriceAreaService priceAreaService;

    @Resource private IIncomeMemoryCacheService incomeMemoryCacheService;

    @Resource private ThreadPoolTaskExecutor threadPoolTaskExecutor;

    @Resource private ElectricPriceService electricPriceService;

    @Resource private StrategyTemplateService strategyTemplateService;

    /** 添加电价/时段配置 */
    @PostMapping("/savePrice")
    @ApiOperation("保存价格配置")
    @PreAuthorize("hasAuthority('/manage/price/update')")
    @Log(module = "MANAGE_PRICE", methods = "MANAGE_PRICE_SAVE", type = OperationType.ADD_SIMPLE)
    public Result<Object> savePrice(@RequestBody List<ElectricPriceEntity> electricPriceList) {
        // 复用一下 这个checkElectricPriceType方法
        electricPriceService.checkElectricPriceType(electricPriceList);
        // 这个为什么不选择  electricPriceService 的 savePrice?
        electricPriceCloudService.savePrice(electricPriceList);
        threadPoolTaskExecutor.submit(
                () -> {
                    if (!electricPriceList.isEmpty()) {
                        String areaId = electricPriceList.get(0).getProjectId();
                        PriceAreaEntity priceAreaEntity = priceAreaService.getById(areaId);
                        if (priceAreaEntity != null) {
                            List<ProjectExtEntity> list =
                                    projectExtService.list(
                                            Wrappers.lambdaQuery(ProjectExtEntity.class)
                                                    .eq(
                                                            ProjectExtEntity::getArea,
                                                            priceAreaEntity.getArea()));
                            if (!list.isEmpty()) {
                                List<ProjectEntity> projectList =
                                        projectService.list(
                                                Wrappers.lambdaQuery(ProjectEntity.class)
                                                        .eq(ProjectEntity::getWhetherDelete, false)
                                                        .in(
                                                                ProjectEntity::getId,
                                                                list.stream()
                                                                        .map(
                                                                                ProjectExtEntity
                                                                                        ::getId)
                                                                        .collect(
                                                                                Collectors
                                                                                        .toList())));
                                for (ProjectEntity projectEntity : projectList) {
                                    incomeMemoryCacheService.updateElectricProfitVO(
                                            projectEntity.getId(),
                                            projectEntity.getProjectName(),
                                            projectEntity.getCreateTime(),
                                            projectEntity.getTimezone());
                                }
                            }
                        }
                    }
                });
        return Result.success();
    }

    /** 查询电价配置 */
    @PostMapping("/getPrice")
    @ApiOperation("查询电价配置")
    @PreAuthorize("hasAuthority('/manage/price/query')")
    public Result<PriceAreaElectricVos> getPrice(
            @ApiParam(required = true, value = "区域id") @RequestParam("areaId") String areaId) {
        // 这个方法 原本只返回 electricPriceList , 现在要返回 electricPriceList  和 这个区域对应的电价类型
        PriceAreaEntity priceAreaEntity = priceAreaService.getById(areaId);
        List<ElectricPriceEntity> electricPriceList =
                electricPriceCloudService.getPriceList(areaId);
        PriceAreaElectricVos electricVos = new PriceAreaElectricVos();
        electricVos.setElectricPriceEntities(electricPriceList);
        electricVos.setPriceType(priceAreaEntity.getPriceType());
        if (ElectricPriceTypeEnum.DYNAMIC_PRICE_PERIOD
                .name()
                .equals(priceAreaEntity.getPriceType())) {
            // 如果是实时电价 把 地区和间隔返回
            electricVos.setElectricPriceArea(priceAreaEntity.getElectricPriceArea());
            electricVos.setElectricPriceSpan(priceAreaEntity.getElectricPriceSpan());
        }
        return Result.success(electricVos);
    }

    @PostMapping("/getRealTimePriceDiagram")
    @ApiOperation("查询电价配置")
    // @PreAuthorize("hasAuthority('/manage/price/getRealTimePriceDiagram')")
    public Result<List<ElectricPriceSystemData.Data>> getRealTimePriceDiagram(
            @ApiParam(value = "区域id") @RequestParam(value = "areaId", required = false)
                    String areaId,
            @RequestParam("startTime") Long startTime,
            @RequestParam(value = "endTime", required = false) Long endTime,
            @ApiParam(value = "模版id 支持管理端模版来查询")
                    @RequestParam(value = "templateId", required = false)
                    String templateId) {
        RealTimePriceRequest request =
                new RealTimePriceRequest().setStartTime(startTime).setEndTime(endTime);
        String priceType = null;
        PriceAreaEntity priceAreaEntity = new PriceAreaEntity();
        if (areaId != null) {
            // check 一下 只有当前项目是 实时电价 才能去查询
            priceAreaEntity = priceAreaService.getById(areaId);
            priceType = priceAreaEntity.getPriceType();
        } else if (templateId != null) {
            StrategyTemplateEntity strategyTemplateEntity =
                    strategyTemplateService.getById(templateId);
            if (strategyTemplateEntity != null
                    && strategyTemplateEntity.getElectricPriceType() != null
                    && strategyTemplateEntity.getCountry() != null
                    && strategyTemplateEntity.getElectricPriceArea() != null
                    && strategyTemplateEntity.getElectricPriceSpan() != null) {
                priceType = strategyTemplateEntity.getElectricPriceType();
                priceAreaEntity = new PriceAreaEntity();
                priceAreaEntity.setElectricPriceSpan(strategyTemplateEntity.getElectricPriceSpan());
                priceAreaEntity.setElectricPriceArea(strategyTemplateEntity.getElectricPriceArea());
                priceAreaEntity.setCountryId(strategyTemplateEntity.getCountry());
            } else {
                throw new ServiceException(ErrorResultCode.ILLEGAL_ARGUMENT.value());
            }
        }
        if (priceType == null
                || !priceType.equals(ElectricPriceTypeEnum.DYNAMIC_PRICE_PERIOD.name())) {
            throw new ServiceException(ErrorResultCode.JUST_ONLY_DYNAMIC_PRICE.value());
        }
        PriceAreaEntity finalPriceAreaEntity = priceAreaEntity;
        List<ElectricPriceSystemData.Data> datas =
                electricPriceService.getRealTimePriceSystemData(
                        request.getStartTime(),
                        request.getEndTime(),
                        new ElectricPriceService.RealTimePriceSystemContext() {
                            @Override
                            public Integer countryId() {
                                return finalPriceAreaEntity.getCountryId();
                            }

                            @Override
                            public String priceArea() {
                                return finalPriceAreaEntity.getElectricPriceArea();
                            }

                            @Override
                            public String span() {
                                return finalPriceAreaEntity.getElectricPriceSpan();
                            }
                        });
        return Result.success(datas);
    }
}
